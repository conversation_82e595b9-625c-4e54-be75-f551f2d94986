package com.ruoyi.common.utils;

import java.io.IOException;

import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.simpleemail.AmazonSimpleEmailService;
import com.amazonaws.services.simpleemail.AmazonSimpleEmailServiceClientBuilder;
import com.amazonaws.services.simpleemail.model.*;
import com.ruoyi.common.core.domain.dto.EmailDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
public class AmazonSESSampleService {

    public static SendEmailResult sendEmail(EmailDTO dto, String ak, String sk, String region, String config) throws Exception {

        BasicAWSCredentials credentials = new BasicAWSCredentials(ak, sk);
        AmazonSimpleEmailService client = AmazonSimpleEmailServiceClientBuilder.standard()
                .withCredentials(new AWSStaticCredentialsProvider(credentials))

                .withRegion(region).build();
        SendEmailRequest request = new SendEmailRequest()
                .withDestination(
                        new Destination().withToAddresses(dto.getReceiveEmail()))
                .withMessage(new Message()

                        .withBody(new Body()
                                .withHtml(new Content().withCharset("UTF-8").withData(dto.getContent())))
                        .withSubject(new Content().withCharset("UTF-8").withData(dto.getSubject()))
                )
                .withSource(dto.getFrom())

                .withConfigurationSetName(config);

        return client.sendEmail(request);


    }
}