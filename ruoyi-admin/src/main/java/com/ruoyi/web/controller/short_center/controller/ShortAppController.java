package com.ruoyi.web.controller.short_center.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.domain.ShortOrder;
import com.ruoyi.domain.ShortRunlog;
import com.ruoyi.domain.ShortUser;
import com.ruoyi.service.IShortOrderService;
import com.ruoyi.service.IShortRunlogService;
import com.ruoyi.app.api.service.BusinessFunctionApiService;
import com.ruoyi.service.IShortUserService;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.domain.ShortApp;
import com.ruoyi.service.IShortAppService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * App管理Controller
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
@Api("App管理")
@RestController
@RequestMapping("/short_center/app")
public class ShortAppController extends BaseController
{
    private static final Logger log = LoggerFactory.getLogger(ShortAppController.class);

    @Autowired
    private IShortAppService shortAppService;

    @Autowired
    private IShortOrderService shortOrderService;

    @Autowired
    private BusinessFunctionApiService businessFunctionApiService;

    @Autowired
    private IShortRunlogService shortRunlogService;

    @Autowired
    private IShortUserService shortUserService;

    /**
     * 查询App管理列表
     */
    @ApiOperation("APP列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "name", value = "应用名字", dataType = "String", dataTypeClass = Integer.class),
            @ApiImplicitParam(name = "icon", value = "应用图标", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "deep_link", value = "深度链接", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "adjust_facebook_token", value = "Adjust 与 Facebook token的追踪token", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "facebook_pixel_id", value = "Facebook Pixel ID", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "facebook_access_token", value = "Facebook访问令牌", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "company", value = "公司名字", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "status", value = "状态（0正常 1停用）", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "create_by", value = "创建者", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "create_time", value = "创建时间", dataType = "Date", dataTypeClass = String.class),
            @ApiImplicitParam(name = "update_by", value = "更新者", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "update_time", value = "更新时间", dataType = "Date", dataTypeClass = String.class),
            @ApiImplicitParam(name = "remark", value = "备注", dataType = "String", dataTypeClass = String.class)

    })
    @PreAuthorize("@ss.hasPermi('system:app:list')")
    @GetMapping("/list")
    public TableDataInfo list(ShortApp shortApp)
    {
        // 获取分页参数，如果没有传递pageSize，默认使用20
        PageDomain pageDomain = TableSupport.buildPageRequest();
        if (pageDomain.getPageSize() == null || pageDomain.getPageSize() == 10) {
            // 如果是默认的10，改为20
            PageHelper.startPage(pageDomain.getPageNum() != null ? pageDomain.getPageNum() : 1, 20);
        } else {
            startPage();
        }
        List<ShortApp> list = shortAppService.selectShortAppList(shortApp);
        return getDataTable(list);
    }

    /**
     * 查询App管理列表
     */
    @GetMapping("/list_all")
    public List<ShortApp> listAll()
    {
        return shortAppService.selectShortAppList(new ShortApp());
    }

    /**
     * 导出App管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:app:export')")
    @Log(title = "App管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ShortApp shortApp)
    {
        List<ShortApp> list = shortAppService.selectShortAppList(shortApp);
        ExcelUtil<ShortApp> util = new ExcelUtil<ShortApp>(ShortApp.class);
        util.exportExcel(response, list, "App管理数据");
    }

    /**
     * 获取App管理详细信息
     */
    @ApiOperation("获取app详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "id", dataType = "Integer", dataTypeClass = Integer.class),

    })
    @PreAuthorize("@ss.hasPermi('system:app:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(shortAppService.selectShortAppById(id));
    }

    /**
     * 新增App管理
     */
    @ApiOperation("新增APP")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "name", value = "应用名字", dataType = "String", dataTypeClass = Integer.class),
            @ApiImplicitParam(name = "icon", value = "应用图标", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "deep_link", value = "深度链接", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "adjust_facebook_token", value = "Adjust 与 Facebook token的追踪token", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "facebook_pixel_id", value = "Facebook Pixel ID", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "facebook_access_token", value = "Facebook访问令牌", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "company", value = "公司名字", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "status", value = "状态（0正常 1停用）", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "create_by", value = "创建者", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "create_time", value = "创建时间", dataType = "Date", dataTypeClass = String.class),
            @ApiImplicitParam(name = "update_by", value = "更新者", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "update_time", value = "更新时间", dataType = "Date", dataTypeClass = String.class),
            @ApiImplicitParam(name = "remark", value = "备注", dataType = "String", dataTypeClass = String.class)

    })
    @PreAuthorize("@ss.hasPermi('system:app:add')")
    @Log(title = "App管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ShortApp shortApp)
    {
        shortApp.setCreateBy(getStrUserId());
        return toAjax(shortAppService.insertShortApp(shortApp));
    }

    /**
     * 修改App管理
     */
    @ApiOperation("修改APP")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Id", value = "应用名字", dataType = "Integer", dataTypeClass = Integer.class),
            @ApiImplicitParam(name = "name", value = "应用名字", dataType = "String", dataTypeClass = Integer.class),
            @ApiImplicitParam(name = "icon", value = "应用图标", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "deep_link", value = "深度链接", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "adjust_facebook_token", value = "Adjust 与 Facebook token的追踪token", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "facebook_pixel_id", value = "Facebook Pixel ID", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "facebook_access_token", value = "Facebook访问令牌", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "company", value = "公司名字", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "status", value = "状态（0正常 1停用）", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "create_by", value = "创建者", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "create_time", value = "创建时间", dataType = "Date", dataTypeClass = String.class),
            @ApiImplicitParam(name = "update_by", value = "更新者", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "update_time", value = "更新时间", dataType = "Date", dataTypeClass = String.class),
            @ApiImplicitParam(name = "remark", value = "备注", dataType = "String", dataTypeClass = String.class)

    })
    @PreAuthorize("@ss.hasPermi('system:app:edit')")
    @Log(title = "App管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ShortApp shortApp)
    {
        shortApp.setUpdateBy(getStrUserId());
        return toAjax(shortAppService.updateShortApp(shortApp));
    }

    /**
     * 删除App管理
     */
    @ApiOperation("删除APP")
    @PreAuthorize("@ss.hasPermi('system:app:remove')")
    @Log(title = "App管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(shortAppService.deleteShortAppByIds(ids));
    }

    /**
     * 手动执行Facebook购买事件回传
     * @param orderId 订单ID
     * @return 回传结果
     */
    @ApiOperation(value = "手动回传", notes = "手动回传")
    @GetMapping("/facebookEvent")
    public AjaxResult manualResendFacebookEvent(Long orderId) throws InterruptedException {
        Map<String, Object> result = new HashMap<>();

        try {
            // 查询订单信息
            ShortOrder order = shortOrderService.selectShortOrderById(orderId);
            if (order == null) {
                return AjaxResult.error("订单未找到或不属于此应用");
            }
            
            // 检查是否为邮件营销活动订单，如果是则不允许回传
            if (order.getIsEmailMarketingOrder() != null && order.getIsEmailMarketingOrder() == 1) {
                log.info("手动回传请求被拒绝: 邮件营销活动订单不允许回传, 订单ID={}", orderId);
                
                // 更新状态为已处理(跳过回传)
                if(order.getPixelStatus() == null || order.getPixelStatus() == 0) {
                    order.setPixelStatus(4); // 设置状态为"跳过回传(非首单/邮件营销订单)"
                    shortOrderService.updateShortOrder(order);
                    log.info("邮件营销订单状态已更新为已处理(跳过回传): 订单ID={}, 状态=4", orderId);
                }
                
                return AjaxResult.error("邮件营销活动订单不允许回传Facebook事件");
            }
            
            // 记录订单当前状态
            Integer beforeStatus = order.getPixelStatus();
            log.info("开始手动回传订单: ID={}, 当前状态={}", order.getId(), beforeStatus);
            
            // 传递 isManualUpload=true 参数，表示为手动回传，绕过限制
            businessFunctionApiService.sendFacebookEvent(
                    order.getUserId(), 
                    "Purchase", 
                    orderId, 
                    false, 
                    true);

            // 再次验证订单状态是否更新
            ShortOrder verifiedOrder = shortOrderService.selectShortOrderById(orderId);
            if (verifiedOrder != null && verifiedOrder.getPixelStatus() != null && verifiedOrder.getPixelStatus() == 2) {
                log.info("手动回传后状态验证成功: 订单ID={}, 状态=2(手动回传成功)", orderId);
            } else {
                log.warn("手动回传后状态验证未通过: 订单ID={}, 当前状态={}", 
                    orderId, verifiedOrder != null ? verifiedOrder.getPixelStatus() : "null");
                
                // 如果状态未正确更新，尝试强制更新
                if(verifiedOrder != null && (verifiedOrder.getPixelStatus() == null || verifiedOrder.getPixelStatus() != 2)) {
                    verifiedOrder.setPixelStatus(2); // 手动设置为已回传状态
                    shortOrderService.updateShortOrder(verifiedOrder);
                    log.info("手动强制更新订单回传状态: 订单ID={}, 状态=2", orderId);
                }
            }

            result.put("code", 200);
            result.put("msg", "回调成功");
            result.put("data", new HashMap<>());

            return AjaxResult.success(result);
        } catch (Exception e) {
            log.error("手动回传Facebook事件失败: orderId={}, 错误={}", orderId, e.getMessage(), e);
            return AjaxResult.error("回调异常：" + e.getMessage());
        }
    }

    /**
     * 手动执行Facebook注册事件回传
     * @return 回传结果
     */
    @ApiOperation(value = "手动回传", notes = "手动回传")
    @GetMapping("/facebookEventUser")
    public AjaxResult facebookEventUser(Long userId) throws InterruptedException {
        Map<String, Object> result = new HashMap<>();

        try {
            // 查询用户信息
            ShortUser user = shortUserService.selectShortUserById(userId);
            if (user == null) {
                return AjaxResult.error("用户不存在");
            }
            // 传递 isManualUpload=true 参数，表示为手动回传，绕过限制
            boolean success = businessFunctionApiService.sendFacebookEvent(user.getId(), "CompleteRegistration", null, false, true);

            // 更新回传记录
            if (success) {
                user.setPixelStatus(2);  // 手动回传成功
                result.put("code", 200);
                result.put("msg", "CompleteRegistration 事件已触发");
            } else {
                user.setPixelStatus(3);  // 回传失败
                result.put("code", 500);
                result.put("msg", "回传失败，请查看系统日志");
            }

            user.setUpdateTime(new Date());
            shortUserService.updateShortUser(user);

            // 构建详细回传记录
            ShortRunlog manualLog = new ShortRunlog();
            manualLog.setType("手动PIXEL回传");
            manualLog.setState(success ? "1" : "0");
            manualLog.setContent("用户：" + user.getId() +
                    "，回传结果:" + (success ? "成功" : "失败"));
            shortRunlogService.insertShortRunlog(manualLog);

            return AjaxResult.success(result);
        } catch (Exception e) {
            result.put("code", 500);
            result.put("msg", "系统错误: " + e.getMessage());
            return AjaxResult.success(result);
        }
    }
}
