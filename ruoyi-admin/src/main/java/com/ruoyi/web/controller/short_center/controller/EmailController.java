package com.ruoyi.web.controller.short_center.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.dto.ShortEmailSendLogDTO;
import com.ruoyi.service.IShortEmailSendLogService;
import com.ruoyi.vo.ShortEmailSendLogVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apiguardian.api.API;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api("邮件发送数据")
@RestController
@RequestMapping("/email/data/")
public class EmailController {

    @Resource
    private IShortEmailSendLogService shortEmailSendLogService;
    @PostMapping("/emailList")
    @ApiOperation(value = "邮件发送列表")
    public R<Page<ShortEmailSendLogVO>> emailList(@RequestBody ShortEmailSendLogDTO sendLogDTO) {
        return shortEmailSendLogService.emailList(sendLogDTO);
    }
}
