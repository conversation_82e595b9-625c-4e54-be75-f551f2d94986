# 数据源配置
spring:
    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        driverClassName: com.mysql.cj.jdbc.Driver
        druid:
            # 主库数据源
            master:
                url: *******************************************************************************************************************************************************************************************************************
                username: root
                password: Juzhun123321!
            # 从库数据源
            slave:
                # 从数据源开关/默认关闭
                enabled: false
                url:
                username:
                password:
            # 初始连接数
            initialSize: 5
            # 最小连接池数量
            minIdle: 10
            # 最大连接池数量
            maxActive: 20
            # 配置获取连接等待超时的时间
            maxWait: 60000
            # 配置连接超时时间
            connectTimeout: 30000
            # 配置网络超时时间
            socketTimeout: 60000
            # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
            timeBetweenEvictionRunsMillis: 60000
            # 配置一个连接在池中最小生存的时间，单位是毫秒
            minEvictableIdleTimeMillis: 300000
            # 配置一个连接在池中最大生存的时间，单位是毫秒
            maxEvictableIdleTimeMillis: 900000
            # 配置检测连接是否有效
            validationQuery: SELECT 1 FROM DUAL
            testWhileIdle: true
            testOnBorrow: false
            testOnReturn: false
            webStatFilter:
                enabled: true
            statViewServlet:
                enabled: true
                # 设置白名单，不填则允许所有访问
                allow:
                url-pattern: /druid/*
                # 控制台管理用户名和密码
                login-username: ruoyi
                login-password: 123456
            filter:
                stat:
                    enabled: true
                    # 慢SQL记录
                    log-slow-sql: true
                    slow-sql-millis: 1000
                    merge-sql: true
                wall:
                    config:
                        multi-statement-allow: true
    # redis 配置
    redis:
        # 地址
        host: ************
        # 端口，默认为6379
        port: 6379
        # 数据库索引
        database: 15
        # 密码
        password: juzhun2023
        # 连接超时时间
        timeout: 10s
        lettuce:
            pool:
                # 连接池中的最小空闲连接
                min-idle: 0
                # 连接池中的最大空闲连接
                max-idle: 8
                # 连接池的最大数据库连接数
                max-active: 8
                # #连接池最大阻塞等待时间（使用负值表示没有限制）
                max-wait: -1ms

# 添加R2配置项
cloud:
    r2:
        file-prefix: https://testup.veryshortvideos.com/
        access-key: 1b6cae32d2772f116e8cdeffc24341be
        secret-key: 8fb142abff43943760f63a456fb222ea4fa571428f0a295d60e8c4ee285a1d6f
        endpoint: https://ee93a2dd66a3a741438af78442815b3d.r2.cloudflarestorage.com
        bucket-name: testjava
        region: auto  # R2特殊区域标识

# 支付配置
payment:
  use:
    dev: true
pay:
    temp:
        id: 59
test-pid:
    temp:
        id: 62
test-pid-vip:
    temp:
        id: 182
ruoyi:
    downloadPrefix: https://test-java.shortplaycenter.com/files/
    uploadPath: https://test-java.shortplaycenter.com/prod-api/common/upload
