# 项目相关配置
ruoyi:
  # 名称
  name: RuoYi
  # 版本
  version: 3.8.9
  # 版权年份
  copyrightYear: 2025
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: /opt
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: math

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100
    relaxed-query-chars: [ '{', '}', '[', ']' ]

# 日志配置
logging:
  level:
    com.ruoyi: debug
    org.springframework: warn

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: dev # 开发环境
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 500MB
      # 设置总上传的文件大小
      max-request-size: 500MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: false
  webclient:
    connect-timeout: 120000  # 连接超时时间，单位毫秒
    read-timeout: 120000     # 读取超时时间，单位毫秒

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认1天）
  expireTime: 1440

# App端token配置
app:
  # 令牌密钥
  secret: bd67a0562b6f3fb4b7e4b7c39a4d0cffb0dd1efe3e6ceb0b5f2c0bbb37a5d52d
  # 令牌有效期（默认30天）
  expireTime: 43200

## MyBatis配置
#mybatis:
#  # 搜索指定包别名
#  typeAliasesPackage: com.ruoyi.**.domain
#  # 配置mapper的扫描，找到所有的mapper.xml映射文件
#  mapperLocations: classpath*:mapper/**/*Mapper.xml
#  # 加载全局的配置文件
#  configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

# MyBatis Plus配置
mybatis-plus:
  # 搜索指定包别名
  typeAliasesPackage: com.ruoyi.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# Brevo
brevo:
  api-key: "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"
  sender:
    email: "<EMAIL>"  # 已验证的发件邮箱
    name: "Kushort"

# 支付配置
payment:
  # 是否使用开发环境API，会根据spring.profiles.active自动切换
  use:
    dev: true  # 开发环境默认true，生产环境改为false
  api:
    dev:
      url: https://api-demo.airwallex.com
    prod:
      url: https://api.airwallex.com

# 争议处理配置
dispute:
  # 自动接受争议配置
  auto:
    accept:
      # 是否启用自动接受争议功能（只要有争议就自动退款）
      enabled: true
  # 用户黑名单配置
  blacklist:
    user:
      # 是否启用争议用户拉黑功能
      enabled: true

