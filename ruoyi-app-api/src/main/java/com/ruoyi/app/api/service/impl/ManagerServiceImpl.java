package com.ruoyi.app.api.service.impl;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.KeyPair;
import com.ruoyi.app.api.service.ManagerService;
import com.ruoyi.app.api.service.PaymentApiService;
import com.ruoyi.app.api.service.BusinessFunctionApiService;
import com.ruoyi.app.api.vo.MovieDo;
import com.ruoyi.app.api.vo.UserDo;
import com.ruoyi.app.api.vo.VideoDo;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.domain.*;
import com.ruoyi.mapper.ShortRenewSubscribeDataMapper;
import com.ruoyi.mapper.ShortVideoMapper;
import com.ruoyi.service.*;
import com.ruoyi.service.impl.ImageProcessor;
import org.apache.tika.Tika;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.model.PutObjectResponse;
import software.amazon.awssdk.services.s3.model.S3Exception;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.security.MessageDigest;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.time.temporal.ChronoUnit;
import java.util.stream.Stream;

import static com.ruoyi.common.utils.Threads.sleep;

/**
 * 管理类信息服务实现
 */
@Service
public class ManagerServiceImpl implements ManagerService {

    private static final Logger log = LoggerFactory.getLogger(ManagerServiceImpl.class);

    @Autowired
    private IShortMovieService shortMovieService;

    @Autowired
    private IShortVideoService shortVideoService;

    @Autowired
    private IShortUserService shortUserService;

    @Autowired
    private IShortVipService shortVipService;

    @Autowired
    private IShortOrderService shortOrderService;

    @Autowired
    private PaymentApiService paymentApiService;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private IShortRunlogService shortRunlogService;

    @Autowired
    private IShortSemLinkService shortSemLinkService;

    @Autowired
    private IShortExtplatsService shortExtplatsService;

    @Value("${payment.use.dev:false}")
    private boolean useDevApi;
    
    @Value("${payment.api.dev.url:https://api-demo.airwallex.com}")
    private String devApiUrl;
    
    @Value("${payment.api.prod.url:https://api.airwallex.com}")
    private String prodApiUrl;

    @Autowired
    private IShortRenewSubscribeDataService shortRenewSubscribeDataService;

    @Resource
    private ShortRenewSubscribeDataMapper shortRenewSubscribeDataMapper;

    @Autowired
    private IShortIntermedStatusService shortIntermedStatusService;

    @Value("${cloud.r2.bucket-name}")
    private String bucketName;

    @Value("${cloud.r2.file-prefix}")
    private String filePrefix;

    @Autowired
    private S3Client s3Client;

    @Autowired
    private ShortVideoMapper shortVideoMapper;

    @Value("${test-pid.temp.id}")
    private String testPidTempId;

    @Value("${test-pid-vip.temp.id}")
    private String testPidTempIdVip;

    @Autowired
    private BusinessFunctionApiService businessFunctionApiService;

    public ManagerServiceImpl() {
    }


    @Override
    public AjaxResult addMovie(MovieDo movieDo) {
        Map<String, Object> map = new HashMap<String, Object>();
        ShortMovie shortMovie = shortMovieService.findByName(movieDo.getName());
        if(null != shortMovie) {
            map.put("id", shortMovie.getId());
            return AjaxResult.error(400,"数据已存在",map);
        }
        else{
            ShortMovie movie = new ShortMovie();
            movie.setAppId(Long.valueOf(movieDo.getApp_id()));
            movie.setName(movieDo.getName());
            movie.setOldname(movieDo.getOldname());
            if(null != movieDo.getIcon())
                movie.setIcon(movieDo.getIcon());
            else
                movie.setIcon("/media/none.png");
            movie.setDirector(movieDo.getDirector());
            movie.setActors(movieDo.getActors());
            movie.setUpTime(movieDo.getUp_time());
            movie.setTime(String.valueOf(movieDo.getTime()));
            movie.setNum(movieDo.getNum());
            movie.setRating(movieDo.getRating());
            movie.setDescription(movieDo.getDescription());
            movie.setContent(movieDo.getContent());
            movie.setUnitCoin(movieDo.getUnit_coin());
            movie.setVipNum("10");
            movie.setSource(movieDo.getSource());
            movie.setIsVip(false);
            movie.setState("0");
            movie.setUnitCoin(80);
            movie.setCreateTime(DateUtils.getNowDate());
            movie.setUpdatetime(DateUtils.getNowDate());
            shortMovieService.addAppMovie(movie);
            map.put("id",movie.getId());
        }
        return AjaxResult.success("成功", map);

    }

    @Override
    public AjaxResult addVideo(VideoDo videoDo) {
        ShortVideo shortVideo = shortVideoService.findByUrl(videoDo.getUrl());
        if(null != shortVideo) {
            return AjaxResult.error(400,shortVideo.getUrl()+" => 数据已存在");
        }
        else{
            ShortVideo video = new ShortVideo();
            video.setMovieId(StringUtils.isEmpty(videoDo.getMid())? null :Long.valueOf(videoDo.getMid()));
            video.setPic(videoDo.getPic());
            video.setUrl(videoDo.getUrl());
            if(StringUtils.isEmpty(videoDo.getNum()))
                video.setNum(1);
            else
                video.setNum(Integer.valueOf(videoDo.getNum()));
            video.setCoin(80);
            video.setIsVip(false);
            video.setState("0");
            video.setCreateTime(DateUtils.getNowDate());
            video.setUpdatetime(DateUtils.getNowDate());
            video.setAddtime(DateUtils.getNowDate());
            shortVideoService.addAppVideo(video);
        }
        return AjaxResult.success("添加成功");
    }

    @Override
    public AjaxResult getAllSubUsers() {
//        List<ShortUser> list = shortUserService.getAllSubUsers();
//        List<UserDo> targetList =list.stream()
//                .map(source -> {
//                    UserDo userDo = new UserDo();
//                    userDo.setUser_id(source.getId());
//                    userDo.setPrice(String.valueOf(source.getPrice()));
//                    userDo.setExpire_time(source.getExpireTime());
//                    // 其他属性映射
//                    return userDo;
//                }).collect(Collectors.toList());
        List<ShortUser> distinctOrders =subUser();
        List<UserDo> targetList =distinctOrders.stream()
                .map(source -> {
                    UserDo userDo = new UserDo();
                    userDo.setUser_id(source.getId());
                    userDo.setPrice(String.valueOf(source.getPrice()));
                    userDo.setExpire_time(source.getExpireTime());
                    // 其他属性映射
                    return userDo;
                }).collect(Collectors.toList());
        return AjaxResult.success("获取所有订阅用户成功, 满足条件的用户有"+targetList.size()+"个", targetList);
    }

    public  List<ShortUser> subUser(){
        List<ShortUser> list = shortUserService.getAllByTypeAndExpireTime("订阅续费");
        List<ShortUser> listD = shortUserService.getAllByTypeAndExpireTime("订阅");
        list.addAll(listD);
        // 根据userId分组，并选择每组中updateTime最大的记录
        List<ShortUser> distinctOrders = list.stream()
                .collect(Collectors.toMap(
                        ShortUser::getId,      // 以userId作为key
                        order -> order,        // value为order对象本身
                        (existing, replacement) ->
                                existing.getUpdatetime().compareTo(replacement.getUpdatetime()) > 0
                                        ? existing
                                        : replacement  // 保留updateTime更大的记录
                ))
                .values()
                .stream()
                .collect(Collectors.toList());


        List<ShortUser> resub = shortUserService.getRenewSubscribe();

        // --- 新增代码：提取resub中的所有ID ---
        Set<Long> excludeIds = resub.stream()
                .map(ShortUser::getId)
                .collect(Collectors.toSet());
        // --- 修改原合并逻辑 ---
        List<ShortUser> uniqueList = distinctOrders.stream()
                .filter(user -> !excludeIds.contains(user.getId()))  // 过滤掉distinctOrders中与resub重复的ID
                .collect(Collectors.toList());



        uniqueList.sort(Comparator.comparing(ShortUser::getUpdatetime).reversed());

        return uniqueList;
    }

    @Override
    public AjaxResult getAutoSubUser(String uid) {
        if (StringUtils.isNotEmpty(uid)) {
            // 处理uid前缀
            if (uid.length() > 4 ) {
                uid = uid.substring(4);
            }
        }
        ShortUser user = shortUserService.selectShortUserById(Long.valueOf(uid));
        if (null == user) {
            log.error("用户不存在",uid);
            return AjaxResult.error(400,"用户不存在");
        }

        // 检查请求次数
        if ((null != user.getPushNum() && user.getPushNum() >= 3) || (null != user.getUnsub() && user.getUnsub() == 1)) {
            if(null != user.getUnsub() && user.getUnsub() != 1)
                shortUserService.updateUnsub(user.getId());
            return AjaxResult.error(400,String.format("用户：%d，自动续订失败，用户请求次数超过三次或取消订阅", user.getId()));
        }
//        if (null != user.getUnsub() && user.getUnsub() == 1) {
//            return AjaxResult.error(400,String.format("用户：%d，自动续订失败，用户取消订阅", user.getId()));
//        }

//        # 判断用户是否在允许的续订时间范围内
//        # push_num 表示已尝试续订的次数,每次尝试续订后会增加7天的等待期
//        # 例如: push_num=1时需等待7天,push_num=2时需等待14天,以此类推
//        if user.push_num and timezone.now() < user.expire_time + timedelta(days=7 * user.push_num):
//        return JsonResponse({'code': 400, 'msg': f'用户：{user.id}，自动续订失败，用户订阅时间超过{7 * user.push_num}天'})

        if (null == user.getExpireTime()) {
            return AjaxResult.error(400,"用户订阅到期时间为空");
        }
        LocalDateTime localDateTime = user.getExpireTime().toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
        // 检查续订时间范围
        if (null != user.getPushNum() && user.getPushNum().intValue()>0 && null != user.getExpireTime() && LocalDateTime.now().isBefore(localDateTime.plus(Duration.ofDays(7 * user.getPushNum())))) {
            return AjaxResult.error(400,"用户："+user.getId()+"，自动续订失败，用户订阅时间超过"+7 * user.getPushNum()+"天");
        }


        // 获取 Token
        String token = shortExtplatsService.getPaymentToken(user.getAppId() != null ? user.getAppId().toString() : null);
        if (token == null || token.isEmpty()) {
            return AjaxResult.error(405,"没有拿到 token 无法发送订单要求");
        }

        if(null != user.getExpireTime()){
            if(localDateTime.isAfter(LocalDateTime.now().plusDays(1))){
                return AjaxResult.error(400,"用户不满足续订条件");
            }
        }


        String isOk = user.getExpireTime() + ", 满足条件";


        // 获取用户的最后一次订阅订单
        ShortOrder originalOrder = null;
        ShortOrder lastSubRe = shortOrderService.findFirstByUserAndPay_typeAndStatusOrderByUpdatedAtDesc(user.getId(), "订阅续费", "SUCCEEDED");
        if(null != lastSubRe) {
            originalOrder = lastSubRe;
        }else{
            ShortOrder lastSub = shortOrderService.findFirstByUserAndPay_typeAndStatusOrderByUpdatedAtDesc(user.getId(), "订阅", "SUCCEEDED");
            if(null != lastSub)
                originalOrder = lastSub;
        }

        if (null == originalOrder ) {
            return AjaxResult.error(400,"没有找到订阅订单");
        }

        //定死 营销邮件发送后  续费订阅
        if(originalOrder.getAmount().compareTo(new BigDecimal("0.88")) == 0){
            originalOrder.setLinkId(Long.valueOf("3666"));
            originalOrder.setVipId(Long.valueOf("190"));
            originalOrder.setAmount(new BigDecimal("14.99"));
        }

        ShortVip shortVipTest = shortVipService.selectShortVipById(originalOrder.getVipId());
        if(null != shortVipTest && testPidTempId.equals(String.valueOf(shortVipTest.getPayTemplateId())) ){
            originalOrder.setVipId(Long.valueOf(testPidTempIdVip));
            originalOrder.setAmount(new BigDecimal("29.99"));
        }

        ShortRenewSubscribeData shortRenewSubscribeData = new ShortRenewSubscribeData();
        shortRenewSubscribeData.setCreateTime(DateUtils.getNowDate());

        LocalDateTime now = LocalDateTime.now(); // 直接获取当前时间
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String formattedDate = now.format(formatter);
        shortRenewSubscribeData.setLogDate(formattedDate);
        shortRenewSubscribeData.setUserId(user.getId());
        shortRenewSubscribeData.setPhoneVersion(user.getPhoneVersion());
        shortRenewSubscribeData.setRenewAmount(originalOrder.getAmount());

        ShortVip shortVip = shortVipService.selectShortVipById(originalOrder.getVipId());
        if(null != shortVip)
            shortRenewSubscribeData.setRenewType(shortVip.getSubscriptionType());


        // 解析 pay_info
        try {
            // 获取用户支付信息
            String payInfo = user.getPayInfo();

            if (payInfo == null || payInfo.isEmpty()) {
                return AjaxResult.error(400,"用户ID: {"+user.getId()+"} 没有支付信息");
            }
            // 解析支付信息
            String paymentConsentId = null;
            String customerId = null;

            // payInfo是JSON字符串格式的数组
            ObjectMapper mapper = new ObjectMapper();
            List<Map<String, String>> payInfoList = mapper.readValue(payInfo,
                    mapper.getTypeFactory().constructCollectionType(List.class, Map.class));

            if (!payInfoList.isEmpty()) {
                Map<String, String> firstPayInfo = payInfoList.get(0);
                paymentConsentId = firstPayInfo.get("payment_consent_id");
                customerId = firstPayInfo.get("customer_id");
            }

            if (paymentConsentId == null || customerId == null) {
                return AjaxResult.error(400,"用户ID: {"+user.getId()+"} 支付信息不完整");
            }

            // 生成随机订单号
            String merchantOrderId = "Merchant_Order" + paymentApiService.generateRandomCode();

            // 创建支付意向
            Map<String, Object> payRequestBody = new HashMap<>();
            payRequestBody.put("request_id", merchantOrderId);
            payRequestBody.put("amount", originalOrder.getAmount());
            payRequestBody.put("currency", "USD");
            payRequestBody.put("merchant_order_id", merchantOrderId);
            payRequestBody.put("customer_id", customerId);

            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + token);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(payRequestBody, headers);


            ResponseEntity<Map> payResponse = restTemplate.postForEntity(
                    (useDevApi ? devApiUrl : prodApiUrl) + "/api/v1/pa/payment_intents/create",
                    request,
                    Map.class
            );
            log.info("创建意向支付返回："+payResponse);
            if (payResponse.getStatusCodeValue() == 201 && payResponse.getBody() != null) {
                Map<String, Object> responseBody = payResponse.getBody();

                String paymentIntentId = (String) responseBody.get("id");
                String clientSecret = (String) responseBody.get("client_secret");
                String currency = (String) responseBody.get("currency");
                String status = (String) responseBody.get("status");


                Long now_link = 0L;
                if(null != originalOrder.getLinkId()){
                    ShortSemLink shortLink = shortSemLinkService.selectShortSemLinkById(originalOrder.getLinkId());
                    if(null != shortLink){
                        now_link = shortLink.getId();
                    }
                }

                // 创建订单记录
                ShortOrder order = new ShortOrder();
                order.setUserId(user.getId());
                order.setVipId(originalOrder.getVipId());
                order.setAppId(user.getAppId());
                order.setPayType("订阅续费");
                order.setOrdersn(merchantOrderId);
                order.setMerchantOrderId(merchantOrderId);
                order.setPaymentIntentId(paymentIntentId);
                order.setRequestId((String) payRequestBody.get("request_id"));
                order.setPaymentCurrency("USD");
                order.setPaymentAmount(originalOrder.getAmount());
                order.setAmount(originalOrder.getAmount());
                order.setCurrency(currency);
                order.setStatus(status);
                order.setClientSecret(clientSecret);

                order.setAdid(originalOrder.getAdid());
                order.setLinkId(now_link);
                order.setLinkTime(originalOrder.getLinkTime());
                order.setOther(originalOrder.getOther());

                order.setCreatedAt(DateUtils.getNowDate());
                order.setUpdatedAt(DateUtils.getNowDate());
                order.setCreateTime(DateUtils.getNowDate());
                order.setUpdateTime(DateUtils.getNowDate());
                order.setPixelId(originalOrder.getPixelId());
                shortOrderService.insertShortOrder(order);

                log.info("为用户 {} 创建订阅意向订单成功: {}", user.getId(), merchantOrderId);

                // 确认付款意向
                Map<String, Object> confirmRequestBody = new HashMap<>();
                confirmRequestBody.put("request_id", generateRandomCode());
                confirmRequestBody.put("payment_consent_id", paymentConsentId);

                HttpEntity<Map<String, Object>> confirmRequest = new HttpEntity<>(confirmRequestBody, headers);

                ResponseEntity<Map> confirmResponse = restTemplate.postForEntity(
                        (useDevApi ? devApiUrl : prodApiUrl) + "/api/v1/pa/payment_intents/" + paymentIntentId + "/confirm",
                        confirmRequest,
                        Map.class
                );
                // 记录日志并处理结果
                ShortRunlog runlog = new ShortRunlog();
                runlog.setType("自动续订");
                runlog.setCreateTime(DateUtils.getNowDate());
                runlog.setUpdateTime(DateUtils.getNowDate());
//                runlog.setA(LocalDateTime.now());




                if ("SUCCEEDED".equals(confirmResponse.getBody().get("status"))) {
                    runlog.setState("1");
                    runlog.setContent(String.format("用户："+user.getId()+"，自动续订成功，支付金额："+confirmResponse.getBody().get("amount")+"，支付方式："+((Map) ((Map) confirmResponse.getBody().get("latest_payment_attempt")).get("payment_method")).get("type")));
                    runlog.setNote(confirmResponse.getBody().toString());
                    shortRunlogService.insertShortRunlog(runlog);

                    ShortRenewSubscribeData subscribeData = shortRenewSubscribeDataService.selectByUserIdStatus(user.getId(),"");
                    shortRenewSubscribeData.setOrderStatus("SUCCEEDED");
                    if(null == subscribeData){
                        shortRenewSubscribeData.setRenewCount(1);
                        shortRenewSubscribeData.setPayErrorCount(0);
                        List<ShortRenewSubscribeData> saveList = new ArrayList<>();
                        saveList.add(shortRenewSubscribeData);
                        shortRenewSubscribeDataMapper.insertBatch(saveList);
                    }else{
                        if(!formattedDate.equals(subscribeData.getLogDate())){
                            shortRenewSubscribeData.setRenewCount(subscribeData.getRenewCount()+1);
                            shortRenewSubscribeData.setPayErrorCount(0);
                            List<ShortRenewSubscribeData> saveList = new ArrayList<>();
                            saveList.add(shortRenewSubscribeData);
                            shortRenewSubscribeDataMapper.insertBatch(saveList);
                        }

                    }
                    return AjaxResult.error(200,String.format("处理完成，"+token+", 用户id:"+uid+", "+isOk+", 续订详细信息："+confirmResponse.getBody()));
                } else if ("issuer_declined".equals(confirmResponse.getBody().get("code"))) {
                    runlog.setState("0");
                    runlog.setContent(String.format("用户："+user.getId()+"，自动续订失败，用户卡余额不足"));
                    runlog.setNote(confirmResponse.getBody().toString());
                    shortRunlogService.insertShortRunlog(runlog);

                    user.setPushNum(user.getPushNum() == null ? 1 : user.getPushNum() + 1);
                    shortUserService.updateShortUser(user);


                    ShortRenewSubscribeData subscribeData = shortRenewSubscribeDataService.selectByUserIdStatus(user.getId(),"");
                    shortRenewSubscribeData.setOrderStatus("REQUIRES_PAYMENT_METHOD");
                    if(null == subscribeData){
                        shortRenewSubscribeData.setRenewCount(1);
                        shortRenewSubscribeData.setPayErrorCount(0);
                        List<ShortRenewSubscribeData> saveList = new ArrayList<>();
                        saveList.add(shortRenewSubscribeData);
                        shortRenewSubscribeDataMapper.insertBatch(saveList);
                    }else{
                        if(!formattedDate.equals(subscribeData.getLogDate())){
                            shortRenewSubscribeData.setRenewCount(subscribeData.getRenewCount()+1);
                            shortRenewSubscribeData.setPayErrorCount(0);
                            List<ShortRenewSubscribeData> saveList = new ArrayList<>();
                            saveList.add(shortRenewSubscribeData);
                            shortRenewSubscribeDataMapper.insertBatch(saveList);
                        }

                    }

                    return AjaxResult.error(400,String.format("用户："+user.getId()+"，自动续订失败，用户卡余额不足，详细信息："+ confirmResponse.getBody()));
                } else {
                    runlog.setState("0");
                    runlog.setContent(String.format("用户："+user.getId()+"，自动续订失败"));
                    runlog.setNote(confirmResponse.getBody().toString());
                    shortRunlogService.insertShortRunlog(runlog);

                    user.setPushNum(user.getPushNum() == null ? 1 : user.getPushNum() + 1);
                    shortUserService.updateShortUser(user);

                    ShortRenewSubscribeData subscribeData = shortRenewSubscribeDataService.selectByUserIdStatus(user.getId(),"");
                    shortRenewSubscribeData.setOrderStatus("REQUIRES_PAYMENT_METHOD");
                    if(null == subscribeData){
                        shortRenewSubscribeData.setRenewCount(1);
                        shortRenewSubscribeData.setPayErrorCount(0);
                        List<ShortRenewSubscribeData> saveList = new ArrayList<>();
                        saveList.add(shortRenewSubscribeData);
                        shortRenewSubscribeDataMapper.insertBatch(saveList);
                    }else{
                        if(!formattedDate.equals(subscribeData.getLogDate())){
                            shortRenewSubscribeData.setRenewCount(subscribeData.getRenewCount()+1);
                            shortRenewSubscribeData.setPayErrorCount(0);
                            List<ShortRenewSubscribeData> saveList = new ArrayList<>();
                            saveList.add(shortRenewSubscribeData);
                            shortRenewSubscribeDataMapper.insertBatch(saveList);
                        }

                    }

                    return AjaxResult.error(400,String.format("用户："+user.getId()+"，自动续订失败，详细信息："+confirmResponse.getBody()));
                }

            } else {

                user.setPushNum(user.getPushNum() == null ? 1 : user.getPushNum() + 1);
                shortUserService.updateShortUser(user);

                ShortRenewSubscribeData subscribeData = shortRenewSubscribeDataService.selectByUserIdStatus(user.getId(),"");
                shortRenewSubscribeData.setOrderStatus("REQUIRES_PAYMENT_METHOD");
                if(null == subscribeData){
                    shortRenewSubscribeData.setRenewCount(1);
                    shortRenewSubscribeData.setPayErrorCount(0);
                    List<ShortRenewSubscribeData> saveList = new ArrayList<>();
                    saveList.add(shortRenewSubscribeData);
                    shortRenewSubscribeDataMapper.insertBatch(saveList);
                }else{
                    if(!formattedDate.equals(subscribeData.getLogDate())){
                        shortRenewSubscribeData.setRenewCount(subscribeData.getRenewCount()+1);
                        shortRenewSubscribeData.setPayErrorCount(0);
                        List<ShortRenewSubscribeData> saveList = new ArrayList<>();
                        saveList.add(shortRenewSubscribeData);
                        shortRenewSubscribeDataMapper.insertBatch(saveList);
                    }

                }
                return AjaxResult.error(400,"创建支付意向失败");
            }

        } catch (Exception e) {

            user.setPushNum(user.getPushNum() == null ? 1 : user.getPushNum() + 1);
            shortUserService.updateShortUser(user);

            ShortRenewSubscribeData subscribeData = shortRenewSubscribeDataService.selectByUserIdStatus(user.getId(),"");
            shortRenewSubscribeData.setOrderStatus("REQUIRES_PAYMENT_METHOD");
            if(null == subscribeData){
                shortRenewSubscribeData.setRenewCount(1);
                shortRenewSubscribeData.setPayErrorCount(0);
                List<ShortRenewSubscribeData> saveList = new ArrayList<>();
                saveList.add(shortRenewSubscribeData);
                shortRenewSubscribeDataMapper.insertBatch(saveList);
            }else{
                if(!formattedDate.equals(subscribeData.getLogDate())){
                    shortRenewSubscribeData.setRenewCount(subscribeData.getRenewCount()+1);
                    shortRenewSubscribeData.setPayErrorCount(0);
                    List<ShortRenewSubscribeData> saveList = new ArrayList<>();
                    saveList.add(shortRenewSubscribeData);
                    shortRenewSubscribeDataMapper.insertBatch(saveList);
                }

            }
            e.printStackTrace();
            return AjaxResult.error(400,e.getMessage());
        }

    }

    @Override
    public AjaxResult getautoSub(String publicKey) throws InterruptedException {

        int count = shortIntermedStatusService.countByName("auto_sub");
        if(count>0)
            return AjaxResult.success("自动化订阅续费执行中，请勿重复操作");

        ShortIntermedStatus shortIntermedStatus = new ShortIntermedStatus();
        shortIntermedStatus.setName("auto_sub");
        shortIntermedStatusService.insertShortIntermedStatus(shortIntermedStatus);




        try {
            String privateKey = "-----BEGIN OPENSSH PRIVATE KEY-----\n" +
                    "b3BlbnNzaC1rZXktdjEAAAAABG5vbmUAAAAEbm9uZQAAAAAAAAABAAAAMwAAAAtzc2gtZW\n" +
                    "QyNTUxOQAAACDdx+eInOus2G8pGSS+paM8QV5SkQdAXGh/e0Z7pRaDRwAAAJiIp66uiKeu\n" +
                    "rgAAAAtzc2gtZWQyNTUxOQAAACDdx+eInOus2G8pGSS+paM8QV5SkQdAXGh/e0Z7pRaDRw\n" +
                    "AAAEDJW6rFB140gGyRJZM+mqoIFj8HAGrGpnuWj+sCJa/06N3H54ic66zYbykZJL6lozxB\n" +
                    "XlKRB0BcaH97RnulFoNHAAAAE2Zlbmd4aW41MjIwQDE2My5jb20BAg==\n" +
                    "-----END OPENSSH PRIVATE KEY-----";

            // 2. 加载密钥对
            KeyPair keyPair = KeyPair.load(new JSch(), privateKey.getBytes(), null);
            byte[] derivedPublicKeyBlob = keyPair.getPublicKeyBlob();

            // 3. 解析用户公钥（按空格分割并解码Base64）
            String[] publicKeyParts = publicKey.split(" ");
            if (publicKeyParts.length < 2) {
                throw new IllegalArgumentException("Invalid public key format");
            }
            byte[] userPublicKeyBlob = Base64.getDecoder().decode(publicKeyParts[1]);

            // 4. 安全比对（避免时序攻击）
            boolean isMatch = MessageDigest.isEqual(derivedPublicKeyBlob, userPublicKeyBlob);
            if(isMatch){
                log.info("验证密钥正确");

//                //改完放开注释
                List<ShortUser> list = subUser();
                //执行更新设置同步状态为 1
               // shortUserService.batchUpdateSynStatus(list);

                List<UserDo> targetList =list.stream()
                        .map(source -> {
                            UserDo userDo = new UserDo();
                            userDo.setUser_id(source.getId());
                            userDo.setPrice(String.valueOf(source.getPrice()));
                            userDo.setExpire_time(source.getExpireTime());
                            // 其他属性映射
                            return userDo;
                        }).collect(Collectors.toList());

                log.info("自动订阅获取到的所有数据："+targetList.size());
                for (int i = 0; i < targetList.size(); i++) {
                    log.info("执行id："+targetList.get(i).getUser_id()+"targetList.get(i):"+targetList.get(i));
                    Thread.sleep(2000);
                    getAutoSubUser(1111+String.valueOf(targetList.get(i).getUser_id()));
                }

            }

            shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
            return AjaxResult.success("操作成功");

        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("status", "error");
            error.put("message", "Validation failed: " + e.getMessage());
            shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
            return AjaxResult.error("操作失败");
        }


//        return AjaxResult.success("操作成功");
    }

    @Override
    public AjaxResult testAllSub(String publicKey) {

        int count = shortIntermedStatusService.countByName("auto_sub");
        if(count>0)
            return AjaxResult.success("自动化订阅续费执行中，请勿重复操作");

        ShortIntermedStatus shortIntermedStatus = new ShortIntermedStatus();
        shortIntermedStatus.setName("auto_sub");
        shortIntermedStatusService.insertShortIntermedStatus(shortIntermedStatus);


        try {
            String privateKey = "-----BEGIN OPENSSH PRIVATE KEY-----\n" +
                    "b3BlbnNzaC1rZXktdjEAAAAABG5vbmUAAAAEbm9uZQAAAAAAAAABAAAAMwAAAAtzc2gtZW\n" +
                    "QyNTUxOQAAACDdx+eInOus2G8pGSS+paM8QV5SkQdAXGh/e0Z7pRaDRwAAAJiIp66uiKeu\n" +
                    "rgAAAAtzc2gtZWQyNTUxOQAAACDdx+eInOus2G8pGSS+paM8QV5SkQdAXGh/e0Z7pRaDRw\n" +
                    "AAAEDJW6rFB140gGyRJZM+mqoIFj8HAGrGpnuWj+sCJa/06N3H54ic66zYbykZJL6lozxB\n" +
                    "XlKRB0BcaH97RnulFoNHAAAAE2Zlbmd4aW41MjIwQDE2My5jb20BAg==\n" +
                    "-----END OPENSSH PRIVATE KEY-----";

            // 2. 加载密钥对
            KeyPair keyPair = KeyPair.load(new JSch(), privateKey.getBytes(), null);
            byte[] derivedPublicKeyBlob = keyPair.getPublicKeyBlob();

            // 3. 解析用户公钥（按空格分割并解码Base64）
            String[] publicKeyParts = publicKey.split(" ");
            if (publicKeyParts.length < 2) {
                throw new IllegalArgumentException("Invalid public key format");
            }
            byte[] userPublicKeyBlob = Base64.getDecoder().decode(publicKeyParts[1]);

            // 4. 安全比对（避免时序攻击）
            boolean isMatch = MessageDigest.isEqual(derivedPublicKeyBlob, userPublicKeyBlob);
            if(isMatch){
                log.info("验证密钥正确");
                sleep(8000);
                List<ShortUser> distinctOrders =subUser();
                List<UserDo> targetList =distinctOrders.stream()
                        .map(source -> {
                            UserDo userDo = new UserDo();
                            userDo.setUser_id(source.getId());
                            userDo.setPrice(String.valueOf(source.getPrice()));
                            userDo.setExpire_time(source.getExpireTime());
                            // 其他属性映射
                            return userDo;
                        }).collect(Collectors.toList());

                shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
                return AjaxResult.success("操作成功="+targetList);
            }
            shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
            return AjaxResult.success("操作成功");

        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("status", "error");
            error.put("message", "Validation failed: " + e.getMessage());
            shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
            return AjaxResult.error("操作失败");
        }
    }

    @Override
    public AjaxResult updateIcon(String publicKey) {
        int count = shortIntermedStatusService.countByName("updateIcon");
        if(count>0)
            return AjaxResult.success("处理封面图，请勿重复操作");

        ShortIntermedStatus shortIntermedStatus = new ShortIntermedStatus();
        shortIntermedStatus.setName("updateIcon");
        shortIntermedStatusService.insertShortIntermedStatus(shortIntermedStatus);


        try {
            String privateKey = "-----BEGIN OPENSSH PRIVATE KEY-----\n" +
                    "b3BlbnNzaC1rZXktdjEAAAAABG5vbmUAAAAEbm9uZQAAAAAAAAABAAAAMwAAAAtzc2gtZW\n" +
                    "QyNTUxOQAAACDdx+eInOus2G8pGSS+paM8QV5SkQdAXGh/e0Z7pRaDRwAAAJiIp66uiKeu\n" +
                    "rgAAAAtzc2gtZWQyNTUxOQAAACDdx+eInOus2G8pGSS+paM8QV5SkQdAXGh/e0Z7pRaDRw\n" +
                    "AAAEDJW6rFB140gGyRJZM+mqoIFj8HAGrGpnuWj+sCJa/06N3H54ic66zYbykZJL6lozxB\n" +
                    "XlKRB0BcaH97RnulFoNHAAAAE2Zlbmd4aW41MjIwQDE2My5jb20BAg==\n" +
                    "-----END OPENSSH PRIVATE KEY-----";

            // 2. 加载密钥对
            KeyPair keyPair = KeyPair.load(new JSch(), privateKey.getBytes(), null);
            byte[] derivedPublicKeyBlob = keyPair.getPublicKeyBlob();

            // 3. 解析用户公钥（按空格分割并解码Base64）
            String[] publicKeyParts = publicKey.split(" ");
            if (publicKeyParts.length < 2) {
                throw new IllegalArgumentException("Invalid public key format");
            }
            byte[] userPublicKeyBlob = Base64.getDecoder().decode(publicKeyParts[1]);

            // 4. 安全比对（避免时序攻击）
            boolean isMatch = MessageDigest.isEqual(derivedPublicKeyBlob, userPublicKeyBlob);
            if(isMatch){
                log.info("验证密钥正确-处理封面图");

//                ShortMovie shortMovie = shortMovieService.selectShortMovieById(32L);
                List<ShortMovie> shortMovieList = shortMovieService.getAll();
                for (ShortMovie shortMovie : shortMovieList) {
                    String res = updateIconMovie(shortMovie.getIcon());
                    if(StringUtils.isNotBlank(res)){
                        shortMovieService.updateIconById(shortMovie.getId(),res);

                        List<ShortVideo> shortVideoList = shortVideoMapper.findByMovieId(shortMovie.getId());
                        for (ShortVideo shortVideo : shortVideoList) {
                            String respic = updateIconMovie(shortVideo.getPic());
                            if(StringUtils.isNotBlank(respic)){
                                shortVideoMapper.updatePicById(shortVideo.getId(),respic);
                            }
                        }

                    }
                }



                shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
                return AjaxResult.success("操作成功=");
            }
            shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
            return AjaxResult.success("操作成功");

        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("status", "error");
            error.put("message", "Validation failed: " + e.getMessage());
            shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
            return AjaxResult.error("操作失败");
        }
    }

    @Override
    public AjaxResult updateValidStatus(String uid,String linkId) {
        if (StringUtils.isNotEmpty(uid)) {
            // 处理uid前缀
            if (uid.length() > 4 ) {
                uid = uid.substring(4);
            }
        }
        ShortUser user = shortUserService.selectShortUserById(Long.valueOf(uid));
        if (null != user) {
            if (StrUtil.isNotEmpty(linkId)) {
                if (Objects.isNull(user.getEmailBindDate())) {
                    user.setEmailBindDate(LocalDate.now());
                }
                if (Objects.isNull(user.getEmailBindLink())) {
                    user.setEmailBindLink(Long.parseLong(linkId));
                }
            }
            
            // 检查用户原来的validStatus状态
            Integer previousValidStatus = user.getValidStatus();
            
            // 设置用户为正式注册用户
            user.setValidStatus(1);
            shortUserService.updateShortUser(user);
            
            log.info("用户validStatus已更新: userId={}, 原状态={}, 新状态=1", 
                user.getId(), previousValidStatus);
            
            // 如果用户之前不是正式注册用户(validStatus != 1)，现在变成正式用户，则触发像素回传
            if (previousValidStatus == null || previousValidStatus != 1) {
                // 触发Facebook像素回传（相当于正式注册）
                triggerRegistrationPixel(user);
                log.info("用户首次成为正式注册用户，触发Facebook像素回传: userId={}", user.getId());
            } else {
                log.info("用户已经是正式注册用户，无需重复触发像素回传: userId={}", user.getId());
            }
        }
        return AjaxResult.success("成功");
    }

    /**
     * 触发用户注册的Facebook像素回传
     * 
     * @param user 用户信息
     */
    private void triggerRegistrationPixel(ShortUser user) {
        try {
            log.info("开始触发用户注册像素回传: userId={}, source={}", user.getId(), user.getSource());
            
            // 调用BusinessFunctionApiService中的voFacebook方法
            if (businessFunctionApiService != null) {
                businessFunctionApiService.triggerFacebookPixel(user);
                log.info("Facebook像素回传触发完成: userId={}", user.getId());
            } else {
                log.error("BusinessFunctionApiService未正确注入，无法触发像素回传: userId={}", user.getId());
            }
            
        } catch (Exception e) {
            log.error("触发用户注册像素回传失败: userId={}, error={}", user.getId(), e.getMessage(), e);
            
            // 记录失败日志
            ShortRunlog runlog = new ShortRunlog();
            runlog.setType("PIXEL回传");
            runlog.setState("0");
            runlog.setContent("用户：" + user.getId() + "，注册像素回传触发失败");
            runlog.setNote(e.getMessage());
            shortRunlogService.insertShortRunlog(runlog);
        }
    }

    private String updateIconMovie(String icon){
        String responseUrl = null;
        try {

            MultipartFile file = getMultipartFileFromUrl(icon);

            // 现在可以使用multipartFile了
            //objectKey 对象键（文件在 S3 中的路径和名称）
            String objectKey = "uploads/" + UUID.randomUUID().toString() + "_" + file.getOriginalFilename();

            // 检查图片格式
            String imageType = detectImageType(file);
            if (imageType == null) {
                throw new Exception("仅支持JPG/PNG/JPEG/WEBP/GIF格式");
            }

            // 2. 处理图片
            byte[] processedImage = ImageProcessor.processImage(file.getBytes(), imageType);

            try {
                PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                        .bucket(bucketName)
                        .key(objectKey)
                        .build();

                PutObjectResponse response = s3Client.putObject(
                        putObjectRequest,
                        RequestBody.fromBytes(processedImage)
                );
                //判断文件是否上传成功
                if (response.sdkHttpResponse().isSuccessful()) {
                    log.debug("文件上传成功，构建访问链接");
                    responseUrl = filePrefix + objectKey;
                    System.out.println("responseUrl============"+responseUrl);
                }
                return responseUrl;
            }  catch (S3Exception e) {
//                        throw new RuntimeException("Failed to read the file", e);
            }

        } catch (Exception e) {
//            return "";
        }
        return responseUrl;
    }

    /**
     * 生成随机码
     * @return 随机字符串
     */
    private String generateRandomCode() {
        // 生成15位数字
        StringBuilder numbers = new StringBuilder();
        Random random = new Random();
        for (int i = 0; i < 15; i++) {
            numbers.append(random.nextInt(10));
        }

        // 生成3位字母
        StringBuilder letters = new StringBuilder();
        String alphabet = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
        for (int i = 0; i < 3; i++) {
            letters.append(alphabet.charAt(random.nextInt(alphabet.length())));
        }

        // 组合返回
        return numbers.toString() + letters.toString();
    }

    // 字节数组转十六进制（辅助方法）
    private static String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }

    public static MultipartFile getMultipartFileFromUrl(String imageUrl) throws Exception {
        try {
            URL url = new URL(imageUrl);
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

            // 添加User-Agent等请求头避免403
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestProperty("User-Agent", "Mozilla/5.0");

            try (InputStream inputStream = connection.getInputStream()) {
                byte[] buffer = new byte[4096];
                int bytesRead;

                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
            }

            // 获取文件名
            String fileName = imageUrl.substring(imageUrl.lastIndexOf('/') + 1);

            return new MockMultipartFile(
                    "file",
                    fileName,
                    URLConnection.guessContentTypeFromName(fileName),
                    outputStream.toByteArray()
            );

        } catch (Exception e) {
            throw new RuntimeException("获取远程图片失败: " + e.getMessage());
        }
    }

    public  String detectImageType(MultipartFile file) throws IOException {
        Tika tika = new Tika();
        String mimeType = tika.detect(file.getInputStream());

        switch (mimeType) {
            case "image/jpeg":
                return "jpg";
            case "image/png":
                return "png";
            case "image/gif":
                return "gif";
            case "image/webp":
                return "webp";
            default:
                return null;
        }
    }

}