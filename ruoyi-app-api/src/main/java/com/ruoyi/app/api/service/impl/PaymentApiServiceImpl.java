package com.ruoyi.app.api.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.app.api.service.PaymentApiService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.OrderStatus;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.domain.*;
import com.ruoyi.mapper.ShortEmailSendLogMapper;
import com.ruoyi.mapper.ShortRenewSubscribeDataMapper;
import com.ruoyi.mapper.ShortUserMapper;
import com.ruoyi.service.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 支付相关API服务实现
 */
@Service
public class PaymentApiServiceImpl implements PaymentApiService {

    private static final Logger log = LoggerFactory.getLogger(PaymentApiServiceImpl.class);

    @Autowired
    private IShortVipService vipService;

    @Autowired
    private IShortUserService userService;

    @Autowired
    private IShortOrderService orderService;

    @Autowired
    private IShortPayTemplateAmtService paytemplateAmountService;

    @Autowired
    private IShortRunlogService runlogService;

    @Autowired
    private IShortCoinRecordService coinRecordService;

    @Autowired
    private RestTemplate restTemplate;

    @Value("${payment.use.dev:false}")
    private boolean useDevApi;

    @Value("${payment.api.dev.url:https://api-demo.airwallex.com}")
    private String devApiUrl;

    @Value("${payment.api.prod.url:https://api.airwallex.com}")
    private String prodApiUrl;

    @Autowired
    private IShortExtplatsService shortExtplatsService;

    @Autowired
    private IShortVideoChannelCoinService videoChannelCoinService;

    @Autowired
    private IShortVideoService videoService;

    @Autowired
    private IShortUserUnlockVideoService userUnlockVideoService;

    @Resource
    private ShortEmailSendLogMapper shortEmailSendLogMapper;
    @Value("${pay.temp.id}")
    private Long payTempId;
    @Resource
    private ShortUserMapper shortUserMapper;

    @Value("${test-pid.temp.id}")
    private String testPidTempId;
    /**
     * 创建支付意图
     */
    @Override
    @Transactional
    public Map<String, Object> createIntent(String userId, String amountId, String appId, javax.servlet.http.HttpServletRequest request) {
        log.info("创建支付意图, 用户ID: {}, 金额ID: {}, 应用ID: {}", userId, amountId, appId);

        Map<String, Object> result = new HashMap<>();

        try {
            // 获取邮件营销标识
            String emailFlag = request.getHeader("emailFlag") == null ? "false" : request.getHeader("emailFlag");
            boolean isEmailMarketing = Boolean.valueOf(emailFlag);
            log.info("邮件营销标识: {}", isEmailMarketing);

            // 1. 验证参数
            if (StringUtils.isEmpty(userId) || StringUtils.isEmpty(amountId)) {
                result.put("code", 405);
                result.put("msg", "数据错误");
                return result;
            }

            // 处理用户ID
            String token = request.getHeader("Authorization");
            Long userIdLong = null;

            if (StringUtils.isNotEmpty(token)) {
                // 有token，通过token从数据库获取用户ID
                ShortUser queryUser = new ShortUser();
                queryUser.setToken(token);
                List<ShortUser> users = userService.selectShortUserList(queryUser);
                if (users != null && !users.isEmpty()) {
                    userIdLong = users.get(0).getId();
                    userId = String.valueOf(userIdLong);
                }
            } else {
                // 没有token，从userId去掉前4位
                if (userId.length() > 4) {
                    userId = userId.substring(4);
                    userIdLong = Long.parseLong(userId);
                }
            }

            if (userIdLong == null) {
                result.put("code", 405);
                result.put("msg", "无效的用户ID");
                return result;
            }

            // 2. 根据amountId获取充值产品信息 - 获取充值类型和金币ID
            ShortVip vip = vipService.selectShortVipById(Long.parseLong(amountId));
            if (vip == null) {
                result.put("code", 405);
                result.put("msg", "无效的金额ID");
                return result;
            }

            String upOrSubscribe = vip.getPayType();
            Long coinId = vip.getPayTemplateAmountId();

            // 获取价格和金币值
            // 从PaytemplateAmount表获取价格和金币值
            ShortPayTemplateAmt payTemplateAmt = paytemplateAmountService.selectShortPayTemplateAmtById(coinId);
            if (payTemplateAmt == null) {
                result.put("code", 405);
                result.put("msg", "无效的金额模板ID");
                return result;
            }

            BigDecimal amount = payTemplateAmt.getPrice();
            Long coin = payTemplateAmt.getCoin();

            // 3. 获取支付令牌
            token = shortExtplatsService.getPaymentToken(appId);
            if (StringUtils.isEmpty(token)) {
                result.put("code", 405);
                result.put("msg", "没有拿到token 无法发送订单要求");
                return result;
            }

            // 4. 获取当前用户
            ShortUser user = userService.selectShortUserById(Long.parseLong(userId));
            if (user == null) {
                result.put("code", 405);
                result.put("msg", "用户不存在");
                return result;
            }

            // 5. 获取或创建customer_id
            String customerId = getOrCreateCustomerId(user, token);
            if (StringUtils.isEmpty(customerId)) {
                result.put("code", 405);
                result.put("msg", "创建客户ID失败");
                return result;
            }

            // 6. 查询最新的未支付订单
            ShortOrder existingOrder = orderService.findLatestUnpaidOrder(Long.parseLong(userId), Long.parseLong(appId), Long.parseLong(amountId));
            if (existingOrder != null && StringUtils.isNotEmpty(existingOrder.getPaymentIntentId())) {
                // 存在历史未支付订单，获取最新状态
                Map<String, Object> orderStatus = getPaymentIntentStatus(existingOrder.getPaymentIntentId(), token);
                if (orderStatus != null && orderStatus.containsKey("client_secret")) {
                    String clientSecret = (String) orderStatus.get("client_secret");

                    // 更新订单信息
                    updateOrderInfo(existingOrder, user, clientSecret);

                    // 如果订单中没有金币数量，但现在获取到了，则更新
                    if (coin != null && coin > 0) {
                        // 使用other字段存储金币数量
                        String otherJson = existingOrder.getOther();
                        JSONObject otherData = StringUtils.isEmpty(otherJson) ? new JSONObject() : JSON.parseObject(otherJson);
                        otherData.put("coin", coin);

                        // 确保user.getOther()中的数据也被保存
                        if (StringUtils.isNotEmpty(user.getOther())) {
                            JSONObject userOtherData = JSON.parseObject(user.getOther());
                            // 遍历用户other数据中的所有键值对，添加到订单other中
                            for (Map.Entry<String, Object> entry : userOtherData.entrySet()) {
                                // 如果是coin字段，已经在上面设置过，不再覆盖
                                if (!"coin".equals(entry.getKey())) {
                                    otherData.put(entry.getKey(), entry.getValue());
                                }
                            }
                        }

                        existingOrder.setOther(otherData.toJSONString());
                        orderService.updateShortOrder(existingOrder);
                    }

                    // 构建返回数据
                    Map<String, Object> data = new HashMap<>();
                    data.put("id", existingOrder.getPaymentIntentId());
                    data.put("merchant_order_id", existingOrder.getMerchantOrderId());
                    data.put("client_secret", clientSecret);
                    data.put("currency", existingOrder.getCurrency());
                    data.put("amount", amount);
                    data.put("up_or_subscribe", upOrSubscribe);
                    data.put("customer_id", customerId);

                    result.put("code", 200);
                    result.put("data", data);
                    return result;

                } else {
                    log.error("更新意向订单秘钥接口访问失败");

                    // 使用Runlog记录异常
                    try {
                        // 创建运行日志记录
                        ShortRunlog runlog = new ShortRunlog();
                        runlog.setType("异常报错");
                        runlog.setState("0"); // 0表示异常
                        runlog.setContent("更新意向订单秘钥接口访问失败, 订单ID: " + existingOrder.getPaymentIntentId());
                        runlogService.insertShortRunlog(runlog);
                    } catch (Exception e) {
                        log.error("记录异常日志失败: {}", e.getMessage(), e);
                    }

                    result.put("code", 400);
                    result.put("msg", "更新意向订单秘钥接口访问失败");
                    return result;
                }
            }

            // 7. 创建新的支付意图
            Map<String, Object> paymentIntent = createPaymentIntent(token, amount, customerId);
            if (paymentIntent == null) {
                result.put("code", 405);
                result.put("msg", "创建支付意图失败");
                return result;
            }

            // 8. 保存订单信息，传递邮件营销标识
            saveOrderInfo(paymentIntent, user, vip, appId, amount, coin, isEmailMarketing);

            // 9. 更新用户的VIP ID
            // userService.updateUserVipId(Long.parseLong(userId), Long.parseLong(amountId));

            // 10. 构建返回数据
            Map<String, Object> data = new HashMap<>();
            data.put("id", paymentIntent.get("id"));
            data.put("merchant_order_id", paymentIntent.get("merchant_order_id"));
            data.put("client_secret", paymentIntent.get("client_secret"));
            data.put("currency", paymentIntent.get("currency"));
            data.put("amount", amount);
            data.put("up_or_subscribe", upOrSubscribe);
            data.put("customer_id", customerId);

            result.put("code", 200);
            result.put("data", data);

            log.info("创建意向订单成功");

        } catch (Exception e) {
            log.error("创建支付意图失败: {}", e.getMessage(), e);
            result.put("code", 405);
            result.put("msg", "创建支付意图失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 获取或创建客户ID
     */
    private String getOrCreateCustomerId(ShortUser user, String token) {
        // 检查用户是否已有customer_id
        String payInfo = user.getPayInfo();
        if (StringUtils.isNotEmpty(payInfo)) {
            try {
                // 尝试解析为JSON数组
                if (payInfo.trim().startsWith("[")) {
                    JSONArray payInfoArray = JSON.parseArray(payInfo);
                    if (payInfoArray != null && !payInfoArray.isEmpty()) {
                        JSONObject firstPayInfo = payInfoArray.getJSONObject(0);
                        if (firstPayInfo.containsKey("customer_id")) {
                            String customerId = firstPayInfo.getString("customer_id");
                            if (StringUtils.isNotEmpty(customerId)) { // 确保 customerId 不为空
                                log.info("从用户支付信息获取到客户ID: {}", customerId);
                                return customerId;
                            }
                        }
                    }
                } else {
                    // 尝试解析为JSON对象
                    JSONObject jsonPayInfo = JSON.parseObject(payInfo);
                    if (jsonPayInfo != null && jsonPayInfo.containsKey("customer_id")) {
                        String customerId = jsonPayInfo.getString("customer_id");
                         if (StringUtils.isNotEmpty(customerId)) { // 确保 customerId 不为空
                            log.info("从用户支付信息获取到客户ID: {}", customerId);
                            return customerId;
                         }
                    }
                }
                log.info("用户支付信息中不包含有效的客户ID: {}", payInfo);
            } catch (Exception e) {
                log.error("解析用户支付信息失败: {}, 原始数据: {}", e.getMessage(), payInfo, e);
                // 解析失败，继续尝试创建
            }
        }

        // 如果本地没有有效的 customer_id，则尝试创建或获取客户ID
        try {
            String merchantCustomerId = "merchant_" + user.getHashId();
            log.info("本地无有效客户ID，尝试处理客户ID: {}", merchantCustomerId);

            // 首先尝试查询客户是否已存在
            String customerId = null;
            String clientSecret = null;
            boolean customerExists = false;

            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + token);
            headers.setContentType(MediaType.APPLICATION_JSON);

            try {
                // 尝试获取已存在的客户
                ResponseEntity<String> getResponse = restTemplate.exchange(
                    (useDevApi ? devApiUrl : prodApiUrl) + "/api/v1/pa/customers/" + merchantCustomerId,
                    HttpMethod.GET,
                    new HttpEntity<>(headers),
                    String.class
                );

                if (getResponse.getStatusCodeValue() == 200) {
                    JSONObject customerJson = JSON.parseObject(getResponse.getBody());
                    customerId = customerJson.getString("id");
                    clientSecret = customerJson.getString("client_secret");
                    customerExists = true;
                    log.info("客户ID已存在，获取信息成功: {}", customerId);
                }
            } catch (HttpClientErrorException e) {
                // 404表示客户不存在，这不是错误，而是预期的情况之一
                if (e.getStatusCode().value() != 404) {
                    log.error("查询客户时发生HTTP错误: {} - {}", e.getStatusCode(), e.getResponseBodyAsString());
                }
            } catch (Exception e) {
                log.error("查询客户时发生异常: {}", e.getMessage());
            }

            // 如果客户不存在，创建新客户
            if (!customerExists) {
                String requestId = UUID.randomUUID().toString();
                Map<String, Object> requestBody = new HashMap<>();
                requestBody.put("request_id", requestId);
                requestBody.put("merchant_customer_id", merchantCustomerId);

                HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

                try {
                    ResponseEntity<String> createResponse = restTemplate.exchange(
                        (useDevApi ? devApiUrl : prodApiUrl) + "/api/v1/pa/customers/create",
                        HttpMethod.POST,
                        entity,
                        String.class
                    );

                    if (createResponse.getStatusCodeValue() == 201) {
                        JSONObject jsonResponse = JSON.parseObject(createResponse.getBody());
                        customerId = jsonResponse.getString("id");
                        clientSecret = jsonResponse.getString("client_secret");
                        log.info("成功创建新客户: {}", customerId);
                    } else {
                        log.error("创建客户失败，状态码: {}, 响应: {}",
                                createResponse.getStatusCodeValue(), createResponse.getBody());
                    }
                } catch (Exception e) {
                    log.error("创建客户时发生异常: {}", e.getMessage());
                }
            }

            // 如果成功获取或创建了客户ID，保存到用户信息中
            if (StringUtils.isNotEmpty(customerId)) {
                // 构建支付信息并更新用户 - 使用数组格式
                JSONObject customerInfo = new JSONObject();
                customerInfo.put("type", "airwallex");
                customerInfo.put("customer_id", customerId);
                if (StringUtils.isNotEmpty(clientSecret)) {
                    customerInfo.put("client_secret", clientSecret);
                }

                JSONArray payInfoArray = new JSONArray();
                payInfoArray.add(customerInfo);

                // 将支付信息保存到用户
                String newPayInfo = payInfoArray.toJSONString();
                ShortUser updateUser = new ShortUser();
                updateUser.setId(user.getId());
                updateUser.setPayInfo(newPayInfo);
                userService.updateShortUser(updateUser);

                log.info("成功获取/创建并保存客户ID: {}", customerId);
                return customerId;
            }

            return null; // 如果获取或创建失败，返回null
        } catch (Exception e) {
            // 捕获流程中的任何意外错误
            log.error("获取或创建客户ID过程中发生意外异常: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取支付意图状态
     */
    private Map<String, Object> getPaymentIntentStatus(String paymentIntentId, String token) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + token);
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<String> entity = new HttpEntity<>(headers);

            ResponseEntity<String> response = restTemplate.exchange(
                    (useDevApi ? devApiUrl : prodApiUrl) + "/api/v1/pa/payment_intents/" + paymentIntentId,
                    HttpMethod.GET,
                    entity,
                    String.class
            );

            if (response.getStatusCodeValue() == 200) {
                return JSON.parseObject(response.getBody(), Map.class);
            }
        } catch (Exception e) {
            log.error("获取支付意图状态失败: {}", e.getMessage(), e);
        }
        return null;
    }

    /**
     * 更新订单信息
     */
    private void updateOrderInfo(ShortOrder order, ShortUser user, String clientSecret) {
        // 更新client_secret
        order.setClientSecret(clientSecret);

        // 更新时间
        Date now = new Date();
        order.setUpdatedAt(now);
        order.setUpdateTime(now);

        // 更新用户相关信息
        if (user.getOther() != null) {
            JSONObject otherJson = JSON.parseObject(user.getOther());
            if (otherJson != null && otherJson.containsKey("adId")) {
                order.setAdid(otherJson.getString("adId"));
            }
            // 完整保存用户的other信息
            order.setOther(user.getOther());
        }

        // 更新链接信息
        if (user.getLinkidId() != null) {
            order.setLinkId(user.getLinkidId());
        }
        order.setLinkTime(user.getLinkTime());

        // 保存更新
        orderService.updateShortOrder(order);
    }

    /**
     * 创建支付意图
     */
    private Map<String, Object> createPaymentIntent(String token, BigDecimal amount, String customerId) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + token);
            headers.setContentType(MediaType.APPLICATION_JSON);

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("request_id", generateRandomCode());
            requestBody.put("amount", amount.doubleValue());
            requestBody.put("currency", "USD");
            requestBody.put("merchant_order_id", "Merchant_Order" + generateRandomCode());
            requestBody.put("customer_id", customerId);

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            ResponseEntity<String> response = restTemplate.exchange(
                    (useDevApi ? devApiUrl : prodApiUrl) + "/api/v1/pa/payment_intents/create",
                    HttpMethod.POST,
                    entity,
                    String.class
            );

            return JSON.parseObject(response.getBody(), Map.class);
        } catch (Exception e) {
            log.error("创建支付意图失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 保存订单信息
     */
    private void saveOrderInfo(Map<String, Object> paymentIntent, ShortUser user, ShortVip vip, String appId, BigDecimal amount, Long coin, boolean isEmailMarketing) {
        // 创建订单
        ShortOrder order = new ShortOrder();
        order.setAppId(Long.parseLong(appId));
        order.setUserId(user.getId());
        order.setVipId(vip.getId());
        order.setPayType(vip.getPayType());
        order.setOrdersn((String) paymentIntent.get("merchant_order_id"));
        order.setMerchantOrderId((String) paymentIntent.get("merchant_order_id"));
        order.setPaymentIntentId((String) paymentIntent.get("id"));
        order.setRequestId((String) paymentIntent.get("request_id"));
        order.setPaymentCurrency("USD");
        order.setPaymentAmount(amount);
        order.setAmount(amount);
        order.setCurrency((String) paymentIntent.get("currency"));
        order.setStatus((String) paymentIntent.get("status"));
        order.setClientSecret((String) paymentIntent.get("client_secret"));

        // 设置邮件营销订单标识
        order.setIsEmailMarketingOrder(isEmailMarketing ? 1 : 0);
        if (isEmailMarketing) {
            log.info("创建邮件营销订单，用户ID: {}, 支付意图ID: {}", user.getId(), paymentIntent.get("id"));
        }

        // 设置push_state字段
        order.setPushState("0"); // 初始状态为0，表示未推送

        // 设置时间字段
        Date now = new Date();
        order.setCreatedAt(now);
        order.setUpdatedAt(now);
        order.setCreateTime(now);
        order.setUpdateTime(now);

        // 设置用户相关信息
        if (user.getOther() != null) {
            JSONObject otherJson = JSON.parseObject(user.getOther());
            if (otherJson != null && otherJson.containsKey("adId")) {
                order.setAdid(otherJson.getString("adId"));
            }
        }

        // 设置链接信息
        if (user.getLinkidId() != null) {
            order.setLinkId(user.getLinkidId());
        }
        order.setLinkTime(user.getLinkTime());

        // 设置other字段，存储额外信息（包括金币数量）
        JSONObject otherData = new JSONObject();
        if (StringUtils.isNotEmpty(user.getOther())) {
            otherData = JSON.parseObject(user.getOther());
        }
        if (coin != null && coin > 0) {
            otherData.put("coin", coin);
        }
        order.setOther(otherData.toJSONString());
        order.setPixelId(user.getPixelId());
        // 保存订单
        order.setPixelStatus(null != order.getPixelStatus()? order.getPixelStatus() : 0);
        orderService.insertShortOrder(order);
    }

    /**
     * 生成随机码
     */
    public String generateRandomCode() {
        // 生成15位数字
        StringBuilder numbers = new StringBuilder();
        Random random = new Random();
        for (int i = 0; i < 15; i++) {
            numbers.append(random.nextInt(10));
        }

        // 生成3位字母
        StringBuilder letters = new StringBuilder();
        String alphabet = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
        for (int i = 0; i < 3; i++) {
            letters.append(alphabet.charAt(random.nextInt(alphabet.length())));
        }

        // 组合返回
        return numbers.toString() + letters.toString();
    }

    /**
     * 获取订单状态
     */
    @Override
    public Map<String, Object> getOrderStatus(String merchantOrderId) {
        log.info("获取订单状态, 商户订单ID: {}", merchantOrderId);

        Map<String, Object> result = new HashMap<>();

        try {
            // 1. 验证参数
            if (StringUtils.isEmpty(merchantOrderId)) {
                result.put("code", 400);
                result.put("msg", "Invalid JSON data");
                return result;
            }

            // 2. 查询订单状态
            ShortOrder queryOrder = new ShortOrder();
            queryOrder.setMerchantOrderId(merchantOrderId);
            List<ShortOrder> orders = orderService.selectShortOrderList(queryOrder);

            if (orders != null && !orders.isEmpty()) {
                ShortOrder order = orders.get(0);
                Map<String, Object> data = new HashMap<>();
                data.put("status", order.getStatus());

                result.put("code", 200);
                result.put("data", data);
            } else {
                result.put("code", 404);
                result.put("msg", "Order not found");
            }

        } catch (Exception e) {
            log.error("获取订单状态失败: {}", e.getMessage(), e);
            result.put("code", 500);
            result.put("msg", "Internal server error");
        }

        return result;
    }

    /**
     * 处理支付回调
     * ！！！ 预生产的支付回调走的是生产环境的接口回调
     */
    @Override
    public Map<String, Object> handlePaymentCallback(Map<String, Object> callbackData) {
        log.info("处理支付回调, 回调数据: {}", callbackData);
        Map<String, Object> result = new HashMap<>();

        try {
            // 1. 验证回调数据
            if (callbackData == null || !callbackData.containsKey("data")) {
                result.put("code", 400);
                result.put("msg", "Invalid data");
                return result;
            }

            Map<String, Object> dataObj = (Map<String, Object>) callbackData.get("data");
            if (dataObj == null || !dataObj.containsKey("object")) {
                result.put("code", 400);
                result.put("msg", "Invalid data");
                return result;
            }

            // 记录日志
            ShortRunlog runlog = new ShortRunlog();
            runlog.setType("支付回调信息");
            runlog.setState("1");
            runlog.setContent(JSON.toJSONString(callbackData));
            runlogService.insertShortRunlog(runlog);

            // 提取payment_intent_id、payment_method和status
            String paymentIntentId = null;
            String paymentMethod = null;
            String status = null;
            String paymentConsentId = "";

            // 解析数据结构
            if (dataObj.containsKey("object") && dataObj.get("object") instanceof Map) {
                Map<String, Object> objectData = (Map<String, Object>) dataObj.get("object");
                paymentIntentId = (String) objectData.get("id");
                status = (String) objectData.get("status");
                paymentConsentId = (String) objectData.getOrDefault("payment_consent_id", "");

                if (objectData.containsKey("latest_payment_attempt") &&
                    objectData.get("latest_payment_attempt") instanceof Map) {
                    Map<String, Object> attemptData = (Map<String, Object>) objectData.get("latest_payment_attempt");
                    if (attemptData.containsKey("payment_method") &&
                        attemptData.get("payment_method") instanceof Map) {
                        Map<String, Object> methodData = (Map<String, Object>) attemptData.get("payment_method");
                        paymentMethod = (String) methodData.get("type");
                    }
                }
            }

            if (paymentIntentId == null || paymentMethod == null || status == null) {
                result.put("code", 400);
                result.put("msg", "缺少必要字段");
                return result;
            }

            // 2. 查询订单
            ShortOrder queryOrder = new ShortOrder();
            queryOrder.setPaymentIntentId(paymentIntentId);
            List<ShortOrder> orders = orderService.selectShortOrderList(queryOrder);

            if (orders == null || orders.isEmpty()) {
                result.put("code", 404);
                result.put("msg", "未找到订单");
                return result;
            }
            ShortOrder order = orders.get(0);
             // 如果订单已经处理过（状态已经是SUCCEEDED且支付时间不为空），直接返回成功
             if ("SUCCEEDED".equals(order.getStatus()) && order.getPayTime() != null) {
                log.info("订单已处理过，跳过重复处理, 订单ID: {}, 支付意图ID: {}", order.getId(), paymentIntentId);
                result.put("code", 200);
                result.put("msg", "订单已处理");
                return result;
            }
            if(!"订阅续费".equals(order.getPayType())){
                log.info("非订阅续费订单判断用户和订单深链信息是否一致");
                ShortUser userData = userService.selectShortUserById(order.getUserId());
                //判断当前用户最新的 appId linkId linkTime  adId other pixel_id 与 order表是否一致
                if(null != userData.getLinkidId()
                        && null != userData.getLinkTime()
                        && null != userData.getOther() && StringUtils.isNotEmpty(userData.getOther())
                        && null != userData.getPixelId()
                ){
                    log.info("当前用户是从深链进入注册");
                    if (userData.getAppId().equals(order.getAppId()) &&
                            userData.getLinkidId().equals(order.getLinkId())
                            && userData.getLinkTime().equals(order.getLinkTime())
                            && userData.getOther().equals(order.getOther())
                            && userData.getPixelId().equals(order.getPixelId())) {
                        log.info("当前订单广告链接信息与用户信息一致");
                    }else {
                        //更新订单数据保持与用户信息一致
                        log.info("当前订单广告链接信息与用户信息不一致,执行更新订单操作");
                        order.setAppId(userData.getAppId());
                        order.setLinkId(userData.getLinkidId());
                        order.setLinkTime(userData.getLinkTime());
                        order.setOther(userData.getOther());
                        order.setPixelId(userData.getPixelId());
                        orderService.updateShortOrder(order);
                    }
                }else {
                    log.info("当前用户是从APP直接进入注册");
                }
            }
            // 3. 处理支付成功的情况
            if ("SUCCEEDED".equals(status)) {
                // 区分充值和订阅类型
                if ("充值".equals(order.getPayType())) {
                    // 充值逻辑
                    order.setPaymentMethod(paymentMethod);
                    order.setStatus(status);
                    order.setRawData(JSON.toJSONString(callbackData));
                    order.setUpdateTime(DateUtils.getNowDate());
                    // 设置支付时间
                    order.setPayTime(DateUtils.getNowDate());
                    orderService.updateShortOrder(order);

                    // 通过订单获取用户信息及金币数量
                    Long userId = order.getUserId();
                    Long vipId = order.getVipId();

                    // 获取VIP和金币信息，直接从VIP获取
                    ShortVip vipInfo = vipService.selectShortVipById(vipId);
                    Long payId = vipInfo.getPayTemplateAmountId();
                    ShortPayTemplateAmt payTemplateAmt = paytemplateAmountService.selectShortPayTemplateAmtById(payId);
                    Long amount = payTemplateAmt.getCoin();

                    Long vipCoin = vipInfo.getSendCoin();
                    if (vipCoin == 0) {
                        // 不变
                    } else {
                        amount += vipCoin;
                    }

                    // 给用户充值金币
                    ShortUser user = userService.selectShortUserById(userId);
                    if (user != null && amount > 0) {
                        Long currentCoin = user.getCoin() != null ? user.getCoin() : 0L;
                        user.setCoin(currentCoin + amount);
                        userService.updateShortUser(user);

                        // 添加金币记录
                        logCoinRecord(userId, order.getAppId(), amount, "1");

                        // 更新用户VIP信息
                        user.setVipId(vipId);
                        userService.updateShortUser(user);

                        // 发送Facebook购买事件，传入订单ID
                        sendFacebookPurchaseEvent(userId.toString(), order.getId());
                    }

                    result.put("code", 200);
                    result.put("msg", "订单更新成功");
                    return result;
                } else {
                    // 订阅逻辑
                    order.setPaymentMethod(paymentMethod);
                    order.setStatus(status);
                    order.setRawData(JSON.toJSONString(callbackData));
                    order.setUpdateTime(DateUtils.getNowDate());
                    // 设置支付时间
                    order.setPayTime(DateUtils.getNowDate());
                    orderService.updateShortOrder(order);

                    // 通过订单获取用户信息修改用户订阅状态
                    Long userId = order.getUserId();
                    Long vipId = order.getVipId();

                    // 获取订阅类型
                    ShortVip vip = vipService.selectShortVipById(vipId);
                    String subscriptionType = vip.getSubscriptionType();

                    // 动态计算天数
                    int daysToAdd = Integer.parseInt(subscriptionType);

                    // 获取用户信息
                    ShortUser user = userService.selectShortUserById(userId);

                    if (user != null) {
                        // 判断用户是否已经是订阅用户，计算新的过期时间
                        Date newExpireTime = null;
                        if ("1".equals(user.getIsSubscriber()) && user.getExpireTime() != null &&
                            user.getExpireTime().after(DateUtils.getNowDate())) {
                            // 在现有过期时间基础上添加天数
                            Calendar calendar = Calendar.getInstance();
                            calendar.setTime(user.getExpireTime());
                            calendar.add(Calendar.DAY_OF_YEAR, daysToAdd);
                            newExpireTime = calendar.getTime();
                            } else {
                            // 从当前时间开始计算
                            Calendar calendar = Calendar.getInstance();
                            calendar.add(Calendar.DAY_OF_YEAR, daysToAdd);
                            newExpireTime = calendar.getTime();
                        }

                        // 更新用户订阅状态
                        user.setExpireTime(newExpireTime);
                        user.setIsSubscriber("1");

                        // 更新支付同意信息
                        if (StringUtils.isNotEmpty(user.getPayInfo())) {
                            try {
                                JSONArray payInfoArray = JSON.parseArray(user.getPayInfo());
                                if (payInfoArray != null && !payInfoArray.isEmpty()) {
                                    JSONObject firstPayInfo = payInfoArray.getJSONObject(0);
                                    firstPayInfo.put("payment_consent_id", paymentConsentId);
                                    user.setPayInfo(payInfoArray.toJSONString());
                                }
                            } catch (Exception e) {
                                log.error("更新用户支付同意信息失败: {}", e.getMessage(), e);
                            }
                        }
                        user.setPayByEmailFlag(Boolean.TRUE);
                        // 更新用户VIP信息
                        user.setVipId(vipId);
                        userService.updateShortUser(user);

                        // 订单表添加字段判断是否是邮件营销活动订单,是的话不调回传事件
                        if(order.getIsEmailMarketingOrder() == null || order.getIsEmailMarketingOrder() == 0){
                            // 发送Facebook购买事件，传入订单ID
                            log.info("常规订单，执行像素回传: 用户ID={}, 订单ID={}", userId, order.getId());
                            sendFacebookPurchaseEvent(userId.toString(), order.getId());
                        }else{
                            log.info("邮件营销活动订单，不调回传事件: 用户ID={}, 订单ID={}", userId, order.getId());
                            
                            // 更新订单pixelStatus为4(跳过回传)，表示已处理
                            if(order.getPixelStatus() == null || order.getPixelStatus() == 0) {
                                order.setPixelStatus(4); // 设置状态为"跳过回传(非首单/邮件营销订单)"
                                orderService.updateShortOrder(order);
                                
                                // 验证状态是否成功更新
                                ShortOrder updatedOrder = orderService.selectShortOrderById(order.getId());
                                if(updatedOrder != null && updatedOrder.getPixelStatus() == 4) {
                                    log.info("邮件营销订单状态已更新为已处理(跳过回传): 订单ID={}, 状态=4", order.getId());
                                } else {
                                    log.warn("邮件营销订单状态更新失败: 订单ID={}, 期望状态=4, 实际状态={}", 
                                        order.getId(), 
                                        updatedOrder != null ? updatedOrder.getPixelStatus() : "unknown");
                                }
                            } else if(order.getPixelStatus() != 4) {
                                log.info("邮件营销订单状态已为 {}, 无需更新: 订单ID={}", order.getPixelStatus(), order.getId());
                            }
                        }   

                        // 无论什么来源的订单，都更新邮件发送记录
                        updateEmailSendRecord(user.getId());
                        // 记录订阅续订数据
                        try {
                            LocalDateTime now = LocalDateTime.now(); // 直接获取当前时间
                            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                            String formattedDate = now.format(formatter);

                            // 获取Mapper实例
                            ShortRenewSubscribeDataMapper renewSubscribeDataMapper =
                                    SpringUtils.getBean(ShortRenewSubscribeDataMapper.class);

                            // 查询是否存在记录
                            long count = renewSubscribeDataMapper.countByUserIdAndLogDate(userId, formattedDate);
                            boolean exists = count > 0;

                            if (!exists) {
                                // 只有当天不存在该用户记录时才插入
                                ShortRenewSubscribeData renewData = new ShortRenewSubscribeData();
                                renewData.setCreateTime(DateUtils.getNowDate());
                                renewData.setLogDate(formattedDate);
                                renewData.setUserId(userId);
                                renewData.setPayMethod(paymentMethod);
                                renewData.setPhoneVersion(user.getPhoneVersion() != null ? user.getPhoneVersion() : "Unknown");
                                renewData.setRenewAmount(order.getAmount());
                                renewData.setOrderStatus(status);
                                renewData.setRenewType(subscriptionType);
                                renewData.setRenewCount(0); // 设置续订次数为0
                                renewData.setPayErrorCount(0); // 设置支付失败次数为0

                                // 插入订阅数据记录
                                renewSubscribeDataMapper.insert(renewData);
                                log.info("记录订阅数据成功: userId={}, renewType={}, logDate={}", userId, subscriptionType, formattedDate);
                            } else {
                                log.info("用户今日已有订阅记录，跳过插入: userId={}, logDate={}", userId, formattedDate);
                            }
                        } catch (Exception e) {
                            log.error("记录订阅数据失败: {}", e.getMessage(), e);
                        }
                    }

                    result.put("code", 200);
                    result.put("msg", "订单更新成功");
                    return result;
                }
            } else {
                result.put("code", 400);
                result.put("msg", "无效状态");
                return result;
            }

        } catch (Exception e) {
            log.error("处理支付回调失败: {}", e.getMessage(), e);
            result.put("code", 500);
            result.put("msg", "Internal server error");
        }

        return result;
    }
    private void updateEmailSendRecord(Long userId) {
        ShortEmailSendLog shortEmailSendLog = new ShortEmailSendLog();
        shortEmailSendLog.setUserId(userId);
        shortEmailSendLog.setStatus("1");
        shortEmailSendLogMapper.updateStatusByUserId(shortEmailSendLog);
    }
    /**
     * 记录金币变动
     * @param userId 用户ID
     * @param appId 应用ID
     * @param amount 金币数量
     * @param type 类型：1充值 2消费
     */
    private void logCoinRecord(Long userId, Long appId, Long amount, String type) {
        try {
            // 获取用户信息
            ShortUser user = userService.selectShortUserById(userId);
            if (user == null) {
                log.error("记录金币变动失败: 用户不存在, userId={}", userId);
                return;
            }

            // 创建金币记录实体
            ShortCoinRecord coinRecord = new ShortCoinRecord();
            coinRecord.setUserId(userId);
            coinRecord.setAppId(appId);
            coinRecord.setCoin(amount);
            coinRecord.setConstype(type); // 1:充值, 2:消费
            coinRecord.setStatus("0"); // 0:正常

            // 计算并设置余额
            Long balance = user.getCoin() != null ? user.getCoin() : 0L;
            coinRecord.setBalance(balance);

            // 插入金币记录
            coinRecordService.insertShortCoinRecord(coinRecord);

            log.info("记录金币变动成功: 用户ID={}, 应用ID={}, 金额={}, 类型={}", userId, appId, amount, type);
        } catch (Exception e) {
            log.error("记录金币变动失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 发送Facebook购买事件
     *
     * @param userId 用户ID
     * @param orderId 订单ID
     */
    private void sendFacebookPurchaseEvent(String userId, Long orderId) {
        try {
            // 获取BusinessFunctionApiServiceImpl实例
            BusinessFunctionApiServiceImpl businessFunctionApiService =
                    SpringUtils.getBean(BusinessFunctionApiServiceImpl.class);

            // 调用通用方法发送购买事件，设置为异步执行，并传入订单ID
            businessFunctionApiService.sendFacebookEvent(Long.parseLong(userId), "Purchase", orderId, true, false);

            log.info("调用通用Facebook事件发送方法: userId={}, event=Purchase, orderId={}", userId, orderId);
        } catch (Exception e) {
            log.error("调用Facebook事件发送方法失败: {}", e.getMessage(), e);

            // 记录异常日志
            try {
                ShortRunlog errorLog = new ShortRunlog();
                errorLog.setType("PIXEL回传");
                errorLog.setState("0");
                errorLog.setContent("用户：" + userId + "，支付成功回传失败");
                errorLog.setNote(e.getMessage());
                runlogService.insertShortRunlog(errorLog);
            } catch (Exception logEx) {
                log.error("记录事件错误日志失败", logEx);
            }
        }
    }

    /**
     * 处理订阅回调
     */
    @Override
    public Map<String, Object> handleSubscribeCallback(Map<String, Object> subscribeData) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 记录回调信息到日志
            ShortRunlog runlog = new ShortRunlog();
            runlog.setType("订阅回调");
            runlog.setState("1");
            runlog.setContent(JSON.toJSONString(subscribeData));
            runlogService.insertShortRunlog(runlog);

            result.put("code", 200);
            result.put("msg", "订阅回调成功");

        } catch (Exception e) {
            log.error("处理订阅回调失败: {}", e.getMessage(), e);
            result.put("code", 404);
            result.put("msg", "订阅回调失败");
        }

        return result;
    }

    /**
     * 获取VIP类型数据
     */
    @Override
    public Map<String, Object> getVipList(String templateId,Boolean flag,String userId,String linkAppId) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 参数验证
            if (StringUtils.isEmpty(templateId)) {
                result.put("code", 400);
                result.put("msg", "获取vip类型数据失败");
                return result;
            }
            if(StringUtils.isNotEmpty(userId) && !"-1".equals(userId) ){
                String sub = StrUtil.sub(userId, 4, userId.length());
                Boolean flag1 = shortUserMapper.selectShortUserEmailFlag(Long.parseLong(sub));
                if(flag && !flag1){
                    templateId=String.valueOf(payTempId);
                }
            }

            if(StringUtils.isNotEmpty(linkAppId) &&  Integer.parseInt(linkAppId) == 4){
                templateId = String.valueOf(testPidTempId);
            }
            // 根据充值模板ID查询VIP列表
            ShortVip queryVip = new ShortVip();
            queryVip.setPayTemplateId(Long.parseLong(templateId));
            List<ShortVip> vipList = vipService.selectShortVipList2(queryVip);

            if (vipList != null && !vipList.isEmpty()) {
                // 构建返回数据
                List<Map<String, Object>> dataList = new ArrayList<>();

                for (ShortVip vip : vipList) {
                    Map<String, Object> vipData = new HashMap<>();
                    vipData.put("id", vip.getId());
                    vipData.put("name", vip.getName());

                    // 获取金币信息
                    Long coin = 0L;
                    BigDecimal price = BigDecimal.ZERO;
                     if (vip.getPayTemplateAmountId() != null) {
                         ShortPayTemplateAmt payTemplateAmt = paytemplateAmountService.selectShortPayTemplateAmtById(vip.getPayTemplateAmountId());
                         if (payTemplateAmt != null) {
                             if (payTemplateAmt.getCoin() != null) {
                                 coin = payTemplateAmt.getCoin();
                             }
                             if (payTemplateAmt.getPrice() != null) {
                                 price = payTemplateAmt.getPrice();
                             }
                         }
                     }

                    vipData.put("coin", coin);
                    vipData.put("send_coin", vip.getSendCoin() != null ? vip.getSendCoin() : 0);
                    vipData.put("pay_type", vip.getPayType());

                    vipData.put("subscription_type", vip.getSubscriptionType());

                    vipData.put("price", price);

                    dataList.add(vipData);
                }

                result.put("code", 200);
                result.put("msg", "success");
                result.put("data", dataList);
            } else {
                result.put("code", 400);
                result.put("msg", "获取vip类型数据失败");
            }

        } catch (Exception e) {
            log.error("获取VIP类型数据失败: {}", e.getMessage(), e);
            result.put("code", 400);
            result.put("msg", "获取vip类型数据失败");
        }

        return result;
    }

    /**
     * 获取金币消费记录
     */
    @Override
    public Map<String, Object> getCoinRecords(String token, String uid) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 通过token或uid获取用户ID
            Long userId = null;

            if (StringUtils.isNotEmpty(token)) {
                // 根据token获取用户ID
                ShortUser queryUser = new ShortUser();
                queryUser.setToken(token);
                List<ShortUser> users = userService.selectShortUserList(queryUser);
                if (users != null && !users.isEmpty()) {
                    userId = users.get(0).getId();
                }
            } else if (StringUtils.isNotEmpty(uid)) {
                // 处理uid前缀
                if (uid.length() > 4) {
                    uid = uid.substring(4);
                }
                userId = Long.parseLong(uid);
            }

            if (userId == null) {
                result.put("code", 400);
                result.put("msg", "未提供用户ID");
                return result;
            }

            // 查询用户的金币记录
            ShortCoinRecord queryCoinRecord = new ShortCoinRecord();
            queryCoinRecord.setUserId(userId);
            List<ShortCoinRecord> records = coinRecordService.selectShortCoinRecordList(queryCoinRecord);

            // 对结果按创建时间倒序排序（最新的排在前面）
            records.sort((a, b) -> {
                if (a.getCreateTime() == null && b.getCreateTime() == null) {
                    return 0;
                }
                if (a.getCreateTime() == null) {
                    return 1;
                }
                if (b.getCreateTime() == null) {
                    return -1;
                }
                return b.getCreateTime().compareTo(a.getCreateTime());
            });


            // 构建返回数据
            List<Map<String, Object>> formattedRecords = new ArrayList<>();

            for (ShortCoinRecord record : records) {
                Map<String, Object> recordMap = new HashMap<>();
                recordMap.put("id", record.getId());
                recordMap.put("user", record.getUserId());
                recordMap.put("app", record.getAppId());
                recordMap.put("coin", record.getCoin());
                recordMap.put("balance", record.getBalance());
                recordMap.put("constype", record.getConstype());

                // 格式化时间
                if (record.getCreateTime() != null) {
                    recordMap.put("addtime", record.getCreateTime().toString());
                } else {
                    recordMap.put("addtime", null);
                }

                if (record.getUpdateTime() != null) {
                    recordMap.put("updatetime", record.getUpdateTime().toString());
                } else {
                    recordMap.put("updatetime", null);
                }

                formattedRecords.add(recordMap);
            }

            result.put("code", 200);
            result.put("data", formattedRecords);

        } catch (Exception e) {
            log.error("获取金币消费记录失败: {}", e.getMessage(), e);
            result.put("code", 400);
            result.put("msg", "无效的请求数据");
        }

        return result;
    }

    /**
     * 扣除用户金币
     */
    @Override
    @Transactional
    public Map<String, Object> deductUserCoin(String token, String appId, String uid, Map<String, Object> deductData) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 获取请求参数
            Integer movieId = Integer.parseInt(deductData.get("movie").toString());
            Integer videoId = Integer.parseInt(deductData.get("video").toString());
            String constype = (String) deductData.get("constype");
            String unlockType = (String) deductData.get("unlock_type");
            String kid = (String) deductData.get("kid");

            log.info("扣除用户金币, 参数: movieId={}, videoId={}, constype={}, unlockType={}, kid={}",
                    movieId, videoId, constype, unlockType, kid);

            // 获取用户ID
            Long userId = null;

            if (StringUtils.isNotEmpty(uid)) {
                // 处理uid前缀
                if (uid.length() > 4 ) {
                    uid = uid.substring(4);
                }
                userId = Long.parseLong(uid);
            } else if (StringUtils.isNotEmpty(token)) {
                // 根据token获取用户
                ShortUser queryUser = new ShortUser();
                queryUser.setToken(token);
                List<ShortUser> users = userService.selectShortUserList(queryUser);
                if (users != null && !users.isEmpty()) {
                    userId = users.get(0).getId();
                }
            }

            if (userId == null) {
                result.put("code", 400);
                result.put("msg", "用户ID和金额为必填项");
                return result;
            }

            // 获取扣除金额
            Long deductAmount = null;

            // 不同解锁类型有不同的金币计算逻辑
            if ("1".equals(unlockType)) {
                // 从VideoChannelCoin表获取金币数量
                deductAmount = getChannelVideoCoin(movieId, videoId, kid);
            } else {
                // 从Video表获取金币数量
                deductAmount = getVideoCoin(videoId);
            }

            if (deductAmount == null || deductAmount <= 0) {
                result.put("code", 400);
                result.put("msg", "无效的金额");
                return result;
            }

            // 获取用户信息并锁定行
            ShortUser user = userService.selectShortUserById(userId);

            if (user == null) {
                result.put("code", 404);
                result.put("msg", "用户不存在");
                return result;
            }

            // 检查用户金币是否足够
            if (user.getCoin() < deductAmount) {
                final Long currentCoin = user.getCoin();
                final Long requiredCoin = deductAmount;
                result.put("code", 403);
                result.put("msg", "金币不足");
                result.put("data", new HashMap<String, Object>() {{
                    put("current_coin", currentCoin);
                    put("required_coin", requiredCoin);
                }});
                return result;
            }

            // 扣除金币并保存
            user.setCoin(user.getCoin() - deductAmount);
            userService.updateShortUser(user);

            // 创建金币消费记录
            ShortCoinRecord coinRecord = new ShortCoinRecord();
            coinRecord.setUserId(userId);
            coinRecord.setAppId(Long.parseLong(appId));
            coinRecord.setCoin(deductAmount);
            coinRecord.setBalance(0L); //设置为0
            coinRecord.setConstype(constype);
            coinRecord.setStatus("0"); // 正常状态
            coinRecord.setCreateTime(DateUtils.getNowDate());
            coinRecord.setUpdateTime(DateUtils.getNowDate());
            coinRecordService.insertShortCoinRecord(coinRecord);

            // 创建用户解锁记录
            if ("1".equals(unlockType)) {
                // Python: channel_coin=VideoChannelCoin.objects.get(id=video_id)
                ShortUserUnlockVideo unlockVideo = new ShortUserUnlockVideo();
                unlockVideo.setUserId(userId);
                unlockVideo.setAppId(Long.parseLong(appId));
                unlockVideo.setUnlockType(unlockType);
                unlockVideo.setMovieId(Long.valueOf(movieId));
                unlockVideo.setVideoId(null);
                unlockVideo.setChannelCoinId(Long.valueOf(videoId)); // 直接使用videoId作为channelCoinId
                unlockVideo.setStatus("0"); // 正常状态
                unlockVideo.setCreateTime(DateUtils.getNowDate());
                userUnlockVideoService.insertShortUserUnlockVideo(unlockVideo);

                log.info("用户解锁渠道视频成功: userId={}, movieId={}, channelCoinId={}", userId, movieId, videoId);
            } else {
                // Python: video=Video.objects.get(id=video_id)
                ShortUserUnlockVideo unlockVideo = new ShortUserUnlockVideo();
                unlockVideo.setUserId(userId);
                unlockVideo.setAppId(Long.parseLong(appId));
                unlockVideo.setUnlockType(unlockType);
                unlockVideo.setMovieId(Long.valueOf(movieId));
                unlockVideo.setVideoId(Long.valueOf(videoId));
                unlockVideo.setChannelCoinId(null);
                unlockVideo.setStatus("0"); // 正常状态
                unlockVideo.setCreateTime(DateUtils.getNowDate());
                userUnlockVideoService.insertShortUserUnlockVideo(unlockVideo);

                log.info("用户解锁常规视频成功: userId={}, movieId={}, videoId={}", userId, movieId, videoId);
            }

            // 返回扣除成功结果
            final Long remainingCoin = user.getCoin();
            final Integer finalMovieId = movieId;
            final Integer finalVideoId = videoId;
            result.put("code", 200);
            result.put("msg", "成功");
            result.put("data", new HashMap<String, Object>() {{
                put("remaining_coin", remainingCoin);
                put("movie_id", finalMovieId);
                put("video_id", finalVideoId);
            }});

        } catch (Exception e) {
            log.error("扣除用户金币失败: {}", e.getMessage(), e);
            result.put("code", 500);
            result.put("msg", "扣除用户金币失败");
        }

        return result;
    }

    /**
     * 获取渠道视频金币数量
     */
    private Long getChannelVideoCoin(Integer movieId, Integer videoId, String kid) {
        if (movieId == null || videoId == null) {
            return 0L;
        }

        try {
            // Python使用: VideoChannelCoin.objects.get(movie_id=movie_id, id=video_id, coin_rule_id=kid).coin
            ShortVideoChannelCoin queryParams = new ShortVideoChannelCoin();
            queryParams.setMovieId(Long.valueOf(movieId));
            queryParams.setId(Long.valueOf(videoId));

            if (StringUtils.isNotEmpty(kid)) {
                queryParams.setCoinRuleId(Long.valueOf(kid));
            }

            // 查询视频频道金币配置
            List<ShortVideoChannelCoin> channelCoins = videoChannelCoinService.selectShortVideoChannelCoinList(queryParams);
            if (channelCoins != null && !channelCoins.isEmpty()) {
                return channelCoins.get(0).getCoin().longValue();
            } else {
                log.error("未找到匹配的渠道视频金币记录: movieId={}, videoId={}, kid={}", movieId, videoId, kid);
            }
        } catch (Exception e) {
            log.error("获取频道视频金币数量失败: {}", e.getMessage(), e);
        }

        return 0L; // 默认返回0
    }

    /**
     * 获取视频金币数量
     */
    private Long getVideoCoin(Integer videoId) {
        if (videoId == null) {
            return 0L;
        }

        try {
            // 查询视频信息
            ShortVideo video = videoService.selectShortVideoById(Long.valueOf(videoId));
            return video != null ? video.getCoin().longValue() : 0L;
        } catch (Exception e) {
            log.error("获取视频金币数量失败: {}", e.getMessage(), e);
            return 0L;
        }
    }

    /**
     * 接受支付争议并进行自动退款
     */
    @Override
    @Transactional
    public AjaxResult acceptDispute(String disputeId) {
        log.info("接受支付争议, 争议ID: {}", disputeId);

        try {
            // 首先查询订单信息，获取appId
            ShortOrder disputeOrder = new ShortOrder();
            disputeOrder.setDisputeId(disputeId);
            List<ShortOrder> disputeOrders = orderService.selectShortOrderList(disputeOrder);

            String appId = null;
            if (disputeOrders != null && !disputeOrders.isEmpty()) {
                appId = String.valueOf(disputeOrders.get(0).getAppId());
            }

            // 1. 获取支付令牌（传入appId）
            String token = shortExtplatsService.getPaymentToken(appId);
            if (StringUtils.isEmpty(token)) {
                return AjaxResult.error("获取支付令牌失败");
            }

            // 2. 构建API请求
            String apiUrl = (useDevApi ? devApiUrl : prodApiUrl) + "/api/v1/pa/payment_disputes/" + disputeId + "/accept";

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + token);

            HttpEntity<String> entity = new HttpEntity<>(headers);

            // 3. 发送接受争议请求
            ResponseEntity<String> response = restTemplate.exchange(
                apiUrl,
                HttpMethod.POST,
                entity,
                String.class
            );

            // 4. 处理响应
            if (response.getStatusCode().is2xxSuccessful()) {
                // 查找并更新订单状态
                ShortOrder queryOrder = new ShortOrder();
                queryOrder.setDisputeId(disputeId);
                List<ShortOrder> orders = orderService.selectShortOrderList(queryOrder);

                if (orders != null && !orders.isEmpty()) {
                    ShortOrder order = orders.get(0);
                    order.setDisputeStatus("ACCEPTED");
                    order.setDisputeResult("ACCEPTED");
                    order.setDisputeProcessTime(new Date());
                    order.setStatus(OrderStatus.DISPUTE_REFUNDED.getCode());
                    orderService.updateShortOrder(order);
                }

                return AjaxResult.success("接受争议成功");
            } else {
                log.error("接受争议失败, 响应: {}", response.getBody());
                return AjaxResult.error("接受争议失败: " + response.getBody());
            }
        } catch (HttpClientErrorException e) {
            log.error("接受争议API异常", e);
            return AjaxResult.error("接受争议API异常: " + e.getResponseBodyAsString());
        } catch (Exception e) {
            log.error("接受争议异常", e);
            return AjaxResult.error("接受争议异常: " + e.getMessage());
        }
    }

    /**
     * 挑战支付争议，提供证据
     */
    @Override
    @Transactional
    public AjaxResult challengeDispute(String disputeId, Map<String, Object> evidence) {
        log.info("挑战支付争议, 争议ID: {}", disputeId);

        try {
            // 首先查询订单信息，获取appId
            ShortOrder disputeOrder = new ShortOrder();
            disputeOrder.setDisputeId(disputeId);
            List<ShortOrder> disputeOrders = orderService.selectShortOrderList(disputeOrder);

            String appId = null;
            if (disputeOrders != null && !disputeOrders.isEmpty()) {
                appId = String.valueOf(disputeOrders.get(0).getAppId());
            }

            // 1. 获取支付令牌（传入appId）
            String token = shortExtplatsService.getPaymentToken(appId);
            if (StringUtils.isEmpty(token)) {
                return AjaxResult.error("获取支付令牌失败");
            }

            // 2. 构建API请求
            String apiUrl = (useDevApi ? devApiUrl : prodApiUrl) + "/api/v1/pa/payment_disputes/" + disputeId + "/challenge";

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + token);

            // 构建证据请求体
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(evidence, headers);

            // 3. 发送挑战争议请求
            ResponseEntity<String> response = restTemplate.exchange(
                apiUrl,
                HttpMethod.POST,
                entity,
                String.class
            );

            // 4. 处理响应
            if (response.getStatusCode().is2xxSuccessful()) {
                // 查找并更新订单状态
                ShortOrder queryOrder = new ShortOrder();
                queryOrder.setDisputeId(disputeId);
                List<ShortOrder> orders = orderService.selectShortOrderList(queryOrder);

                if (orders != null && !orders.isEmpty()) {
                    ShortOrder order = orders.get(0);
                    order.setDisputeStatus("CHALLENGED");
                    order.setDisputeResult("CHALLENGED");
                    order.setDisputeProcessTime(new Date());
                    orderService.updateShortOrder(order);
                }

                return AjaxResult.success("挑战争议成功");
            } else {
                log.error("挑战争议失败, 响应: {}", response.getBody());
                return AjaxResult.error("挑战争议失败: " + response.getBody());
            }
        } catch (HttpClientErrorException e) {
            log.error("挑战争议API异常", e);
            return AjaxResult.error("挑战争议API异常: " + e.getResponseBodyAsString());
        } catch (Exception e) {
            log.error("挑战争议异常", e);
            return AjaxResult.error("挑战争议异常: " + e.getMessage());
        }
    }

    /**
     * 处理争议的用户账号
     */
    @Override
    @Transactional
    public AjaxResult handleDisputeUser(Long userId, String disputeId) {
        log.info("处理争议用户账号, 用户ID: {}, 争议ID: {}", userId, disputeId);

        try {
            // 1. 查找用户
            ShortUser user = userService.selectShortUserById(userId);
            if (user == null) {
                return AjaxResult.error("用户不存在");
            }

            // 2. 禁用用户账号
            user.setState("0"); // 0表示禁用
            user.setRemark("因支付争议自动封号，争议ID: " + disputeId);
            user.setUpdateTime(new Date());

            // 3. 保存用户更新
            userService.updateShortUser(user);

            // 4. 记录日志
            ShortRunlog runlog = new ShortRunlog();
            runlog.setType("USER_BANNED");
            runlog.setContent("用户因支付争议被封号: 用户ID=" + userId + ", 争议ID=" + disputeId);
            runlog.setCreateTime(new Date());
            runlogService.insertShortRunlog(runlog);

            return AjaxResult.success("用户账号已禁用");
        } catch (Exception e) {
            log.error("处理争议用户账号异常", e);
            return AjaxResult.error("处理争议用户账号异常: " + e.getMessage());
        }
    }
}
