package com.ruoyi.app.api.service;

import com.ruoyi.app.api.dto.PaymentDisputeDTO;
import com.ruoyi.common.core.domain.AjaxResult;

import java.util.Map;

/**
 * 支付相关API服务接口
 */
public interface PaymentApiService {
    
    /**
     * 创建支付意图
     * 
     * @param userId 用户ID
     * @param amountId 金额ID
     * @param appId 应用ID
     * @param request HTTP请求
     * @return 支付意图信息
     */
    Map<String, Object> createIntent(String userId, String amountId, String appId, javax.servlet.http.HttpServletRequest request);
    
    /**
     * 获取订单状态
     * 
     * @param merchantOrderId 商户订单ID
     * @return 订单状态信息
     */
    Map<String, Object> getOrderStatus(String merchantOrderId);
    
    /**
     * 处理支付回调
     * 
     * @param callbackData 回调数据
     * @return 处理结果
     */
    Map<String, Object> handlePaymentCallback(Map<String, Object> callbackData);
    
    /**
     * 处理订阅回调
     * 
     * @param subscribeData 订阅数据
     * @return 处理结果
     */
    Map<String, Object> handleSubscribeCallback(Map<String, Object> subscribeData);

    /**
     * 获取VIP类型数据
     * 
     * @param templateId 充值模板ID
     * @return 处理结果
     */
    Map<String, Object> getVipList(String templateId,Boolean flag, String userId,String linkAppId);
    
    /**
     * 获取金币消费记录
     * 
     * @param token 用户令牌
     * @param uid 用户ID
     * @return 处理结果
     */
    Map<String, Object> getCoinRecords(String token, String uid);
    
    /**
     * 扣除用户金币
     * 
     * @param token 用户令牌
     * @param appId 应用ID
     * @param uid 用户ID
     * @param deductData 扣除数据
     * @return 处理结果
     */
    Map<String, Object> deductUserCoin(String token, String appId, String uid, Map<String, Object> deductData);

    /**
     * 接受支付争议并进行自动退款
     *
     * @param disputeId 争议ID
     * @return 处理结果
     */
    AjaxResult acceptDispute(String disputeId);

    /**
     * 挑战支付争议，提供证据
     *
     * @param disputeId 争议ID
     * @param evidence 争议证据
     * @return 处理结果
     */
    AjaxResult challengeDispute(String disputeId, Map<String, Object> evidence);

    /**
     * 处理争议的用户账号
     *
     * @param userId 用户ID
     * @param disputeId 争议ID
     * @return 处理结果
     */
    AjaxResult handleDisputeUser(Long userId, String disputeId);

    String generateRandomCode();
} 