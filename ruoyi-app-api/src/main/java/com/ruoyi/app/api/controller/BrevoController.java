package com.ruoyi.app.api.controller;

import brevo.ApiClient;
import brevo.Configuration;
import brevo.auth.ApiKeyAuth;
import brevoApi.TransactionalEmailsApi;
import brevoModel.CreateSmtpEmail;
import brevoModel.SendSmtpEmail;
import brevoModel.SendSmtpEmailSender;
import brevoModel.SendSmtpEmailTo;
import cn.hutool.core.collection.ListUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.ruoyi.app.api.dto.InitUserDTO;
import com.ruoyi.app.api.service.BusinessFunctionApiService;
import com.ruoyi.app.api.service.impl.EmailServiceContext;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.dto.EmailDTO;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.domain.ShortBrevo;
import com.ruoyi.domain.ShortEmailDomain;
import com.ruoyi.domain.ShortMovie;
import com.ruoyi.domain.ShortRunlog;
import com.ruoyi.service.IShortEmailDomainService;
import com.ruoyi.service.IShortMovieService;
import com.ruoyi.service.IShortRunlogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * brevo邮件管理Controller
 */
@Api("brevo邮件管理")
@RestController
@RequestMapping("/api/brevo")
public class BrevoController extends BaseController {

    @Value("${brevo.api-key}")
    private String brevoApiKey;

    @Value("${brevo.sender.email}")
    private String senderEmail;

    @Value("${brevo.sender.name}")
    private String senderName;

    @Autowired
    private IShortRunlogService shortRunlogService;

    @Autowired
    private BusinessFunctionApiService businessFunctionApiService;

    @Autowired
    private IShortMovieService shortMovieService;
    @Autowired
    private EmailServiceContext emailServiceContext;

    @Autowired
    private IShortEmailDomainService shortEmailDomainService;

    /**
     * 新增App管理
     */
    @ApiOperation("发送邮件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "receiveEmail", value = "收件人邮箱", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "userId", value = "收件人名称", dataType = "String", dataTypeClass = String.class),
    })
    @PostMapping("/send")
    public AjaxResult send(@RequestBody InitUserDTO shortBrevo, HttpServletRequest request) throws JsonProcessingException {
        if (StringUtils.isEmpty(shortBrevo.getReceiveEmail()))
            return error("请填写收件人邮箱！");
        if ("".equals(shortBrevo.getReceiveName()))
            shortBrevo.setReceiveName(null);
        if ("".equals(shortBrevo.getSubject()))
            shortBrevo.setSubject(null);
        String appId = request.getHeader("NID");
        String uid = request.getHeader("UID");
        if (StringUtils.isNotEmpty(uid)) {
            // 处理uid前缀
            if (uid.length() > 4) {
                uid = uid.substring(4);
            }
        }
        shortBrevo.setUserId(uid);

        shortBrevo.setAppId(Long.valueOf(appId));
        String oldUserId = businessFunctionApiService.filterUser(shortBrevo);
        if (!oldUserId.equals(shortBrevo.getUserId())) {
            // 生成4位随机数字 混淆用户ID
            // 使用UUID的hashCode值转为字符串并取前4位
            String randomNum = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 4);
            Map<String, String> vars = new HashMap<>();
            vars.put("uId", randomNum + oldUserId);
            vars.put("uniqueId", shortBrevo.getUniqueId());
            return AjaxResult.success(vars);
        }

        ShortMovie shortMovie = shortMovieService.selectShortMovieById(Long.valueOf(shortBrevo.getMovie()));
        return sendWelcomeEmail(shortBrevo, shortMovie, "aws");

    }


    //营销发送邮件--定时
    @GetMapping("/sendEmail")
    public AjaxResult sendEmail(@RequestParam String publicKey) throws InterruptedException {
        return businessFunctionApiService.sendEmail(publicKey);
    }

    //营销发送邮件--定时
    @GetMapping("/sendGeek")
    public AjaxResult sendGeek(@RequestParam String publicKey) throws InterruptedException {
        return businessFunctionApiService.sendGeek(publicKey);
    }


    @ApiOperation("发送邮件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "receiveEmail", value = "收件人邮箱", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "userId", value = "收件人名称", dataType = "String", dataTypeClass = String.class),
    })
    @PostMapping("/send1")
    public AjaxResult send1() throws JsonProcessingException {
//        if(StringUtils.isEmpty(shortBrevo.getReceiveEmail()))
//            return error("请填写收件人邮箱！");
//        if("".equals(shortBrevo.getReceiveName()))
//            shortBrevo.setReceiveName(null);
//        if("".equals(shortBrevo.getSubject()))
//            shortBrevo.setSubject(null);
//        String appId = request.getHeader("NID");
//        String uid = request.getHeader("UID");
//        if (StringUtils.isNotEmpty(uid)) {
//            // 处理uid前缀
//            if (uid.length() > 4 ) {
//                uid = uid.substring(4);
//            }
//        }
//        shortBrevo.setUserId(uid);

//        shortBrevo.setAppId(Long.valueOf(appId));
//        String oldUserId = businessFunctionApiService.filterUser(shortBrevo);
//        if(null != shortBrevo && !oldUserId.equals(shortBrevo.getUserId())){
//            // 生成4位随机数字 混淆用户ID
//            // 使用UUID的hashCode值转为字符串并取前4位
//            String randomNum = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 4);
//            Map<String, String> vars = new HashMap<>();
//            vars.put("uId", randomNum + oldUserId);
//            vars.put("uniqueId", shortBrevo.getUniqueId());
//            return AjaxResult.success(vars);
//        }

        ShortMovie shortMovie = shortMovieService.selectShortMovieById(Long.valueOf("32"));
        // 1. 配置API密钥
        ApiClient defaultClient = Configuration.getDefaultApiClient();
        ApiKeyAuth apiKey = (ApiKeyAuth) defaultClient.getAuthentication("api-key");
        apiKey.setApiKey(brevoApiKey); // 替换为你的Brevo API密钥

        // 2. 初始化邮件API
        TransactionalEmailsApi apiInstance = new TransactionalEmailsApi();

        // 3. 设置发件人
        SendSmtpEmailSender sender = new SendSmtpEmailSender();
        sender.setEmail(senderEmail); // 必须已验证的邮箱
        sender.setName(senderName);

        // 4. 设置收件人
        List<SendSmtpEmailTo> toList = new ArrayList<>();
        SendSmtpEmailTo recipient = new SendSmtpEmailTo();
        recipient.setEmail("<EMAIL>");
        recipient.setName(null);
        toList.add(recipient);

        // 记录日志并处理结果
        ShortRunlog runlog = new ShortRunlog();
        runlog.setType("发送邮件");
        runlog.setCreateTime(DateUtils.getNowDate());
        runlog.setUpdateTime(DateUtils.getNowDate());

        try {
            // 5. 从HTML文件读取内容
            // 修正资源加载代码
            String htmlName = "reg.html";
//            if(StringUtils.isEmpty(shortBrevo.getHtmlName()))
//                htmlName= "reg.html";
//            else
//                htmlName = shortBrevo.getHtmlName();
            ClassPathResource resource = new ClassPathResource("templates/" + htmlName);
            String htmlContent = StreamUtils.copyToString(
                    resource.getInputStream(),
                    StandardCharsets.UTF_8
            );
            // 使用更安全的占位符格式
            htmlContent = htmlContent
                    .replace("${user_name}", "")
                    .replace("${movieurl}", null != shortMovie.getIcon() ? shortMovie.getIcon() : "")
                    .replace("{{movieurl}}", null != shortMovie.getIcon() ? shortMovie.getIcon() : "")
                    .replace("{{user_name}}", ""); // 兼容旧格式

            // 6. 创建邮件内容
            SendSmtpEmail email = new SendSmtpEmail();
            email.setSender(sender);
            email.setTo(toList);
            email.setSubject("Valued Member");
            email.setHtmlContent(htmlContent); // 使用HTML内容

            new Thread(() -> {
                try {
                    // 7. 发送邮件
                    CreateSmtpEmail response = apiInstance.sendTransacEmail(email);
                    System.out.println("邮件发送成功！消息ID: " + response.getMessageId());
                    runlog.setState("1");
                    runlog.setContent(String.format(",邮件发送成功！消息ID: " + response.getMessageId() + "用户Id：" + "78451" +
                            ",邮件主题：" + "Valued Member" + ",收件人名称：" + "<EMAIL>" +
                            ",收件人邮件：" + "<EMAIL>"));
                    runlog.setNote(response.toString());
                    shortRunlogService.insertShortRunlog(runlog);
                } catch (Exception e) {
                    runlog.setState("0");
                    runlog.setContent(String.format(",发送失败：" + e.getMessage() + ",邮件主题：" + "111111" +
                            ",收件人名称：" + "111111" + ",收件人邮件：" + "<EMAIL>"));
                    runlog.setNote(e.toString());
                    shortRunlogService.insertShortRunlog(runlog);
                }
            }).start();

            String randomNum = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 4);
            Map<String, String> vars = new HashMap<>();
            vars.put("uId", randomNum + "78451");
//            vars.put("uniqueId", shortBrevo.getUniqueId());
            return AjaxResult.success(vars);
        } catch (IOException e) {
            System.err.println("读取HTML文件失败: " + e.getMessage());
            runlog.setState("0");
//            runlog.setContent(String.format(",读取HTML文件失败：" + e.getMessage()+",邮件主题："+shortBrevo.getSubject()+",收件人名称："+shortBrevo.getReceiveName()+",收件人邮件："+shortBrevo.getReceiveEmail()));
            runlog.setNote(e.toString());
            shortRunlogService.insertShortRunlog(runlog);

            return AjaxResult.error("读取HTML文件失败: " + e.getMessage());
        } catch (Exception e) {
            System.err.println("发送失败: " + e.getMessage());
            runlog.setState("0");
//            runlog.setContent(String.format(",发送失败：" + e.getMessage()+",邮件主题："+shortBrevo.getSubject()+",收件人名称："+shortBrevo.getReceiveName()+",收件人邮件："+shortBrevo.getReceiveEmail()));
            runlog.setNote(e.toString());
            shortRunlogService.insertShortRunlog(runlog);
            return AjaxResult.error("发送失败: " + e.getMessage());
        }
    }

    private AjaxResult sendWelcomeEmail(InitUserDTO shortBrevo, ShortMovie shortMovie, String type) {
        SendSmtpEmail email = new SendSmtpEmail();
        TransactionalEmailsApi apiInstance = new TransactionalEmailsApi();
        EmailDTO emailDTO = new EmailDTO();

        // 3. 设置发件人
        SendSmtpEmailSender sender = new SendSmtpEmailSender();
        ShortEmailDomain shortEmailDomain = shortEmailDomainService.getrandomDomain(shortBrevo.getAppId());

        if (type.equals("brevo")) {
            // 2. 初始化邮件API
            ApiClient defaultClient = Configuration.getDefaultApiClient();
            ApiKeyAuth apiKey = (ApiKeyAuth) defaultClient.getAuthentication("api-key");
            apiKey.setApiKey(brevoApiKey); // 替换为你的Brevo API密钥

            if(null != shortEmailDomain){
                sender.setEmail(shortEmailDomain.getDomain()); // 必须已验证的邮箱
                sender.setName(shortEmailDomain.getAppName());
            }else{
                sender.setEmail(senderEmail); // 必须已验证的邮箱
                sender.setName(senderName);
            }


            List<SendSmtpEmailTo> toList = new ArrayList<>();
            // 4. 设置收件人
            SendSmtpEmailTo recipient = new SendSmtpEmailTo();
            recipient.setEmail(null != shortBrevo.getReceiveEmail() ? shortBrevo.getReceiveEmail() : null);
            recipient.setName(null != shortBrevo.getReceiveName() ? shortBrevo.getReceiveName() : null);
            toList.add(recipient);
            email.setSender(sender);
            email.setTo(toList);
        } else {
            emailDTO.setReceiveEmail(ListUtil.toList(shortBrevo.getReceiveEmail()));
            emailDTO.setSendChannel("aws");
            emailDTO.setServiceType("awsSimpl");
            emailDTO.setTitle("Kushort");
        }
        // 记录日志并处理结果
        ShortRunlog runlog = new ShortRunlog();
        runlog.setType("发送邮件");
        runlog.setCreateTime(DateUtils.getNowDate());
        runlog.setUpdateTime(DateUtils.getNowDate());

        try {
            // 5. 从HTML文件读取内容
            // 修正资源加载代码
            String htmlName = "";
//            if (type.equals("aws")) {
//                htmlName = "reg.html";
//                if (null != shortMovie && null != shortMovie.getLangId() && shortMovie.getLangId().intValue() == 4)
//                    htmlName = "ESReg.html";
//            } else {
                if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                    htmlName = "reg.html";
                else
                    htmlName = shortBrevo.getHtmlName();

                if (null != shortMovie && null != shortMovie.getLangId() && shortMovie.getLangId().intValue() == 4)
                    htmlName = "ESReg.html";
//            }

            ClassPathResource resource = new ClassPathResource("templates/" + htmlName);
            String htmlContent = StreamUtils.copyToString(
                    resource.getInputStream(),
                    StandardCharsets.UTF_8
            );
            String subject = null != shortBrevo.getSubject() ? shortBrevo.getSubject() : " Valued Member";
            // 使用更安全的占位符格式
            htmlContent = htmlContent
                    .replace("${user_name}", null != shortBrevo.getUser_name() ? shortBrevo.getUser_name() : "")
                    .replace("${movieurl}", null != shortMovie.getIcon() ? shortMovie.getIcon() : "")
                    .replace("{{movieurl}}", null != shortMovie.getIcon() ? shortMovie.getIcon() : "")

                    .replace("${appName}", null != shortEmailDomain.getAppName() ? shortEmailDomain.getAppName() : "")
                    .replace("{{appName}}", null != shortEmailDomain.getAppName() ? shortEmailDomain.getAppName() : "")

                    .replace("${appDomain}", null != shortEmailDomain.getAppDomain() ? shortEmailDomain.getAppDomain() : "")
                    .replace("{{appDomain}}", null != shortEmailDomain.getAppDomain() ? shortEmailDomain.getAppDomain() : "")

                    .replace("{{user_name}}", null != shortBrevo.getUser_name() ? shortBrevo.getUser_name() : ""); // 兼容旧格式
            if (type.equals("brevo")) {
                // 6. 创建邮件内容
                email.setSubject(subject);
                email.setHtmlContent(htmlContent); // 使用HTML内容
            } else {
                emailDTO.setSubject(subject);
                emailDTO.setContent(htmlContent);
            }

            new Thread(() -> {
                try {
                    runlog.setState("1");
                    String content = "";
                    if (type.equals("brevo")) {
                        // 7. 发送邮件
                        CreateSmtpEmail response = apiInstance.sendTransacEmail(email);
                        System.out.println("邮件发送成功！消息ID: " + response.getMessageId());
                        content = String.format(",邮件发送成功！消息ID: " + response.getMessageId() + "用户Id：" + shortBrevo.getUserId() +
                                ",邮件主题：" + shortBrevo.getSubject() + ",收件人名称：" + shortBrevo.getReceiveName() +
                                ",收件人邮件：" + shortBrevo.getReceiveEmail()+",邮件服务商:"+type);
                        runlog.setNote(response.toString());
                    } else if (type.equals("aws")) {
                        emailServiceContext.getService(emailDTO.getServiceType()).sendEmail(emailDTO);
                        content = String.format(",邮件发送成功！用户Id：" + shortBrevo.getUserId() +
                                ",邮件主题：" + shortBrevo.getSubject() +
                                ",收件人邮件：" + shortBrevo.getReceiveEmail()+
                                ",邮件服务商:"+type);
                        runlog.setNote(type);
                    }
                    runlog.setContent(content);
                    shortRunlogService.insertShortRunlog(runlog);
                } catch (Exception e) {
                    runlog.setState("0");
                    runlog.setContent(String.format(",发送失败：" + e.getMessage() + ",邮件主题：" + shortBrevo.getSubject() +
                            ",收件人名称：" + shortBrevo.getReceiveName() + ",收件人邮件：" + shortBrevo.getReceiveEmail())+",邮件服务商:"+type);
                    runlog.setNote(e.toString());
                    shortRunlogService.insertShortRunlog(runlog);
                }
            }).start();

            String randomNum = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 4);
            Map<String, String> vars = new HashMap<>();
            vars.put("uId", randomNum + shortBrevo.getUserId());
            vars.put("uniqueId", shortBrevo.getUniqueId());
            return AjaxResult.success(vars);
        } catch (IOException e) {
            System.err.println("读取HTML文件失败: " + e.getMessage());
            runlog.setState("0");
            runlog.setContent(String.format(",读取HTML文件失败：" + e.getMessage() + ",邮件主题：" + shortBrevo.getSubject() + ",收件人名称：" + shortBrevo.getReceiveName() + ",收件人邮件：" + shortBrevo.getReceiveEmail()));
            runlog.setNote(e.toString());
            shortRunlogService.insertShortRunlog(runlog);

            return AjaxResult.error("读取HTML文件失败: " + e.getMessage());
        } catch (Exception e) {
            System.err.println("发送失败: " + e.getMessage());
            runlog.setState("0");
            runlog.setContent(String.format(",发送失败：" + e.getMessage() + ",邮件主题：" + shortBrevo.getSubject() + ",收件人名称：" + shortBrevo.getReceiveName() + ",收件人邮件：" + shortBrevo.getReceiveEmail()));
            runlog.setNote(e.toString());
            shortRunlogService.insertShortRunlog(runlog);
            return AjaxResult.error("发送失败: " + e.getMessage());
        }
    }

}
