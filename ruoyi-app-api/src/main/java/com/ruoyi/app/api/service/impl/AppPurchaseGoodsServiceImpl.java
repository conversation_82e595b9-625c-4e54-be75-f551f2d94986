package com.ruoyi.app.api.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.auth.oauth2.GoogleCredentials;
import com.ruoyi.app.api.request.ReceiptRequest;
import com.ruoyi.app.api.response.ReceiptResponse;
import com.ruoyi.app.api.service.AppPurchaseGoodsService;
import com.ruoyi.app.api.service.PaymentApiService;
import com.ruoyi.app.api.utils.IAPUtil;
import com.ruoyi.app.api.vo.ShortAppPurchaseGoodsVO;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.OrderStatus;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.domain.*;
import com.ruoyi.mapper.*;
import com.ruoyi.service.IShortCoinRecordService;
import com.ruoyi.service.IShortOrderService;
import com.ruoyi.service.IShortRunlogService;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 苹果内购验证服务
 */
@Service
public class AppPurchaseGoodsServiceImpl implements AppPurchaseGoodsService {

    private static final Logger logger = LoggerFactory.getLogger(AppPurchaseGoodsServiceImpl.class);

    @Autowired
    private ShortOrderMapper shortOrderMapper;

    @Autowired
    private AppPurchaseGoodsMapper appPurchaseGoodsMapper;

    @Autowired
    private AppPurchaseVerifyResultMapper appPurchaseVerifyResultMapper;

    @Autowired
    private ShortAppMapper shortAppMapper;

    @Autowired
    private ShortUserMapper shortUserMapper;

    @Autowired
    private IShortOrderService shortOrderService;

    @Autowired
    private IShortRunlogService shortRunlogService;

    @Autowired
    private IShortCoinRecordService shortCoinRecordService;

    @Autowired
    private PaymentApiService paymentApiService;

    /**
     * 获取内购商品列表
     *
     * @param appId
     * @return
     */
    @Override
    public List<ShortAppPurchaseGoodsVO> getGoodsList(Long appId, String type) {
        ShortApp shortApp = shortAppMapper.selectShortAppById(appId);
        if (shortApp == null) {
            throw new RuntimeException("App is null");
        }
        if (shortApp.getType().equals("1")) {
            throw new RuntimeException("App is deactivate");
        }
        LambdaQueryWrapper<ShortAppPurchaseGoods> eq = Wrappers.lambdaQuery(new ShortAppPurchaseGoods())
                .eq(ShortAppPurchaseGoods::getAppId, appId)
                .eq(ShortAppPurchaseGoods::getAppType, type.equals("ios") ? "Apple" : "Google")
                .eq(ShortAppPurchaseGoods::getStatus, 1);
        List<ShortAppPurchaseGoods> shortAppPurchaseGoods = appPurchaseGoodsMapper.selectList(eq);
        if (CollectionUtils.isEmpty(shortAppPurchaseGoods)) {
            logger.error("App[{}] Goods is null", appId);
        }
        List<ShortAppPurchaseGoodsVO> voList = shortAppPurchaseGoods.stream()
                .map(goods -> {
                    ShortAppPurchaseGoodsVO vo = new ShortAppPurchaseGoodsVO();
                    BeanUtils.copyProperties(goods, vo);
                    return vo;
                })
                .collect(Collectors.toList());
        return voList;
    }

    /**
     * 验证苹果内购收据
     *
     * @param request 请求参数
     * @return 验证结果
     */
    @Override
    public ReceiptResponse verifyAppleReceipt(ReceiptRequest request) {
        String verificationJson = IAPUtil.verifyApple(request);

        ReceiptResponse receiptResponse = new ReceiptResponse();
        receiptResponse.setSuccess(false);

        if (verificationJson == null) {
            logger.error("【失败】苹果内购票据验证失败");
            return receiptResponse;
        }
        // 结果转成json
        JSONObject receiptData = JSONObject.parseObject(verificationJson);
        logger.info("【成功】苹果内购验证返回结果:{}", receiptData.toJSONString());
        Integer status = receiptData.getInteger("status");

        if (status == 0) {
            List<ShortAppPurchaseVerifyResult> appleVerifyResultList = setVerifyAppleResult(request, receiptData, status);

            appleVerifyResultList.forEach(appleVerifyResult -> {
                Long orderId = updateOrderAndUser(appleVerifyResult, request.getUserId());
                /*
                    如果 order_id = null,但是status = 0 [苹果验证成功]，返回前端应该为成功： true
                    已经 saveVerifyResult 中处理了，并记录异常，需要开发排查问题
                 */
                saveVerifyResult(appleVerifyResult, orderId);
            });

            List<String> transactionIds = appleVerifyResultList.stream()
                    .map(ShortAppPurchaseVerifyResult::getTransactionId)
                    .distinct()
                    .collect(Collectors.toList());

            receiptResponse.setValidTransactions(transactionIds);
            receiptResponse.setSuccess(true);

            return receiptResponse;
        } else {
            logError("APPLE_VERIFY_STATUS_NOT_ZERO", "苹果内购验证返回状态码: " + status + ", Request: " + request.toString());
            return receiptResponse;
        }
    }

    private List<ShortAppPurchaseVerifyResult> setVerifyAppleResult(ReceiptRequest request, JSONObject receiptData, Integer status) {
        JSONObject receiptInfo = receiptData.getJSONObject("receipt");

        ArrayList<ShortAppPurchaseVerifyResult> resultList = new ArrayList<>();

        JSONArray inAppList = receiptInfo.getJSONArray("in_app");
        // ios7之前的数据格式
        if (CollectionUtils.isNotEmpty(inAppList)) {
            logger.info("有【in_app】数据:{}", inAppList.toJSONString());
            inAppList.forEach(inApp -> {
                JSONObject inAppJson = (JSONObject) inApp;

                ShortAppPurchaseVerifyResult verifyResult = new ShortAppPurchaseVerifyResult();
                verifyResult.setReceipt(request.getReceiptData());// 保存前端传递的收据数据
                verifyResult.setAppType("Apple");
                verifyResult.setJsonObject(String.valueOf(receiptData));
                verifyResult.setStatusCode(receiptData.getInteger("status"));
                verifyResult.setEnvironment(receiptData.getString("environment"));

                verifyResult.setAdamId(receiptInfo.getString("adam_id"));
                verifyResult.setAppItemId(receiptInfo.getString("app_item_id"));
                verifyResult.setBundleId(receiptInfo.getString("bundle_id"));
                verifyResult.setApplicationVersion(receiptInfo.getString("application_version"));
                verifyResult.setReceiptCreationDate(receiptInfo.getString("receipt_creation_date"));
                verifyResult.setRequestDate(receiptInfo.getString("request_date"));

                verifyResult.setQuantity(inAppJson.getString("quantity"));
                verifyResult.setProductId(inAppJson.getString("product_id"));
                verifyResult.setTransactionId(inAppJson.getString("transaction_id"));
                verifyResult.setPurchaseDate(inAppJson.getString("purchase_date"));

                resultList.add(verifyResult);
            });
        }
        // ios7之后的数据格式
        if (CollectionUtils.isEmpty(inAppList)) {
            logger.info("无【in_app】数据:{}", receiptInfo.toJSONString());

            ShortAppPurchaseVerifyResult verifyResult = new ShortAppPurchaseVerifyResult();
            verifyResult.setReceipt(request.getReceiptData());// 保存前端传递的收据数据
            verifyResult.setAppType("Apple");
            verifyResult.setJsonObject(String.valueOf(receiptData));
            verifyResult.setStatusCode(receiptData.getInteger("status"));
            verifyResult.setEnvironment(receiptData.getString("environment"));

            verifyResult.setAdamId(receiptInfo.getString("adam_id"));
            verifyResult.setAppItemId(receiptInfo.getString("app_item_id"));
            verifyResult.setBundleId(receiptInfo.getString("bundle_id"));
            verifyResult.setApplicationVersion(receiptInfo.getString("application_version"));
            verifyResult.setReceiptCreationDate(receiptInfo.getString("receipt_creation_date"));
            verifyResult.setRequestDate(receiptInfo.getString("request_date"));

            verifyResult.setQuantity(receiptInfo.getString("quantity"));
            verifyResult.setProductId(receiptInfo.getString("product_id"));
            verifyResult.setTransactionId(receiptInfo.getString("transaction_id"));
            verifyResult.setPurchaseDate(receiptInfo.getString("purchase_date"));

            resultList.add(verifyResult);
        }
        return resultList;
    }

    private void saveVerifyResult(ShortAppPurchaseVerifyResult result, Long orderId) {
        // 更新订单时发现异常，返回orderId为Null，但是苹果票据验证成功【status=0】，所以返回前端应该是成功，这边只能做日志记录，方便客诉排查
        if (orderId == null) {
            logError("APPLE_VERIFY_SUCCESS_BUT_ORDER_ERROR", "更新订单失败，苹果返回结果：" + result.getJsonObject());
            return;
        }

        LambdaQueryWrapper<ShortAppPurchaseVerifyResult> resultEq = Wrappers.lambdaQuery(ShortAppPurchaseVerifyResult.class)
                .eq(ShortAppPurchaseVerifyResult::getOrderId, orderId)
                .eq(ShortAppPurchaseVerifyResult::getStatusCode, 0);
        ShortAppPurchaseVerifyResult shortAppPurchaseVerifyResults = appPurchaseVerifyResultMapper.selectOne(resultEq);
        if (shortAppPurchaseVerifyResults != null) {
            logger.warn("苹果流水id[{}]已经验证，跳过重复操作", result.getTransactionId());
            logError("DUPLICATE_VERIFY", "苹果流水id[" + result.getTransactionId() + "]已经验证，跳过重复操作");
            return;
        }

        // 苹果没返回商品id，【苹果异常，一般不会出现】
        if (StringUtils.isEmpty(result.getProductId())) {
            logger.error("商品id为空");
            logError("APPLE_PRODUCT_ID_EMPTY", "商品id为空");
            return;
        }

        ShortOrder shortOrder = shortOrderMapper.selectShortOrderById(orderId);

        LambdaQueryWrapper<ShortAppPurchaseGoods> eq = Wrappers.lambdaQuery(ShortAppPurchaseGoods.class)
                .eq(ShortAppPurchaseGoods::getProductId, result.getProductId())
                .eq(ShortAppPurchaseGoods::getStatus, 1);
        ShortAppPurchaseGoods goods = appPurchaseGoodsMapper.selectOne(eq);

        if (goods == null) {// 苹果的商品没有入库或被禁用，需要及时入库
            logger.error("商品id【{}】不存在", result.getProductId());
            logError("APPLE_PRODUCT_ID_NOT_EXIST", "商品id【" + result.getProductId() + "】不存在");
        } else {
            // 判断商品的vip id，是否与订单vip id一致，防止维护商品表人为失误填错
            if (!Objects.equals(shortOrder.getVipId(), goods.getVipId())) {
                logger.error("id is not equal");
                logError("APPLE_ID_NOT_EQUAL", "订单vipId【{" + shortOrder.getVipId() + "}】与商品vipId【{" + goods.getVipId() + "}】不一致");
            }
        }

        result.setOrderId(shortOrder.getId());
        result.setOrderType("充值");// 目前只有充值
        result.setAppId(shortOrder.getAppId());

        // 保存 response
        logger.info("开始保存：订单[{}]验证结果", shortOrder.getId());
        int insert = appPurchaseVerifyResultMapper.insert(result);
        if (insert <= 0) {
            logger.error("【失败】保存订单[{}]验证结果失败", shortOrder.getId());
            logError("APPLE_VERIFY_RESULT_SAVE_FAIL", "保存订单[" + shortOrder.getId() + "]验证结果失败");
        }
    }


    /**
     * 记录错误日志到系统日志表
     *
     * @param type    错误类型
     * @param content 错误内容
     */
    private static void logError(String type, String content) {
        try {
            // 获取ShortRunlogService实例
            IShortRunlogService runlogService = SpringUtils.getBean(IShortRunlogService.class);

            ShortRunlog errorLog = new ShortRunlog();
            errorLog.setType("APPLE_IAP_" + type);
            errorLog.setState("0"); // 0表示异常

            StringBuilder logContent = new StringBuilder(content);
            errorLog.setContent(logContent.toString());
            errorLog.setCreateTime(new Date());

            runlogService.insertShortRunlog(errorLog);
        } catch (Exception e) {
            logger.error("记录错误日志失败", e);
        }
    }

    private Long updateOrderAndUser(ShortAppPurchaseVerifyResult verifyResult, Long userId) {
        ShortOrder queryOrder = new ShortOrder();
        queryOrder.setOther("%\"productId\":\"" + verifyResult.getProductId() + "\"%");
        queryOrder.setUserId(userId);
        queryOrder.setStatus(OrderStatus.SUCCEEDED.getCode());
        List<ShortOrder> shortOrders = shortOrderMapper.selectListLikeProductId(queryOrder);

        LambdaQueryWrapper<ShortAppPurchaseVerifyResult> resultEq = Wrappers.lambdaQuery(ShortAppPurchaseVerifyResult.class)
                .eq(ShortAppPurchaseVerifyResult::getTransactionId, verifyResult.getTransactionId())
                .eq(ShortAppPurchaseVerifyResult::getStatusCode, 0);
        ShortAppPurchaseVerifyResult shortAppPurchaseVerifyResults = appPurchaseVerifyResultMapper.selectOne(resultEq);

        // 没有待支付订单，验证结果表中没有数据，【数据异常丢失】，返回null
        if (CollectionUtils.isEmpty(shortOrders) && shortAppPurchaseVerifyResults == null) {
            logger.warn("【异常】用户【{}】不存在待支付订单&也没有苹果流水【{}】的结果数据", userId, verifyResult.getTransactionId());
            logError("APPLE_ORDER_NOT_EXIST", "用户【" + userId + "】不存在待支付订单&也没有苹果流水【" + verifyResult.getTransactionId() + "】的结果数据");
            return null;
        }
        // 有待支付订单，验证结果表中有数据，【数据异常重复】，返回null
        if (CollectionUtils.isNotEmpty(shortOrders) && shortAppPurchaseVerifyResults != null) {
            logger.warn("【异常】用户【{}】存在待支付订单【{}】&也有苹果流水【{}】的结果数据", userId, shortOrders.get(0).getId(), verifyResult.getTransactionId());
            logError("APPLE_ORDER_NOT_EXIST", "用户【" + userId + "】存在待支付订单&也有苹果流水【" + verifyResult.getTransactionId() + "】的结果数据");
            return null;
        }
        // 没有待支付订单，但验证结果表有数据，【正常，但该请求不是第一次】，返回结果表中的order id
        if (CollectionUtils.isEmpty(shortOrders) && shortAppPurchaseVerifyResults != null) {
            logger.info("【已经校验，防重】用户【{}】不存在待支付订单，但有苹果流水【{}】的结果数据", userId, verifyResult.getTransactionId());
            return shortAppPurchaseVerifyResults.getOrderId();
        }
        // 下面应该处理【有待支付订单】但【无验证表的结果数据】，【正常，该请求为第一次】
        ShortOrder shortOrder = shortOrders.get(0);
        logger.info("苹果内购验证成功，开始更新订单 {} 和用户信息", shortOrder.getId());

        // todo 重复无效代码，因为上面获取的是PENDING状态的订单，所以不会出现下面的情况
        if ("SUCCEEDED".equals(shortOrder.getStatus())) {
            logger.warn("订单[{}]已经处理过，跳过重复处理", shortOrder.getId());
            return null;
        }

        shortOrder.setPayTime(DateUtils.getNowDate());
        shortOrder.setStatus(OrderStatus.SUCCEEDED.getCode());
        try {
            JSONObject otherData = StringUtils.isNotEmpty(shortOrder.getOther()) ? JSONObject.parseObject(shortOrder.getOther()) : new JSONObject();
            otherData.put("appleTransactionId", verifyResult.getTransactionId());
            otherData.put("appleVerifyEnvironment", verifyResult.getEnvironment());
            shortOrder.setOther(otherData.toJSONString());
        } catch (Exception e) {
            logger.error("更新订单other字段失败 for orderId: {} (Apple)", shortOrder.getId(), e);
        }
        shortOrderMapper.updateShortOrder(shortOrder);

        ShortUser shortUser = shortUserMapper.selectShortUserById(shortOrder.getUserId());
        Long goodsId = null;
        try {
            if (StringUtils.isNotEmpty(shortOrder.getOther())) {
                JSONObject otherData = JSONObject.parseObject(shortOrder.getOther());
                if (otherData != null && otherData.containsKey("goodsId")) {
                    goodsId = otherData.getLong("goodsId");
                }
            }
        } catch (Exception e) {
            logger.error("解析订单 other 字段中的 goodsId 失败, orderId: {} (Apple)", shortOrder.getId(), e);
            logError("APPLE_UPDATE_ORDER_USER_FAIL", "解析goodsId失败. OrderID: " + shortOrder.getId());
            return shortOrder.getId();
        }

        if (goodsId == null) {
            logger.error("订单 other 字段中未找到 goodsId, orderId: {} (Apple)", shortOrder.getId());
            logError("APPLE_UPDATE_ORDER_USER_FAIL", "goodsId为空. OrderID: " + shortOrder.getId());
            return shortOrder.getId();
        }

        ShortAppPurchaseGoods shortAppPurchaseGoods = appPurchaseGoodsMapper.selectById(goodsId);
        if (shortAppPurchaseGoods == null) {
            logger.error("商品[{}]不存在, orderId: {} (Apple)", goodsId, shortOrder.getId());
            logError("APPLE_UPDATE_ORDER_USER_FAIL", "商品不存在. GoodsID: " + goodsId + ", OrderID: " + shortOrder.getId());
            return shortOrder.getId();
        }

        if (shortUser == null) {
            logger.error("用户[{}]不存在, orderId: {} (Apple)", shortOrder.getUserId(), shortOrder.getId());
            logError("APPLE_UPDATE_ORDER_USER_FAIL", "用户不存在. UserID: " + shortOrder.getUserId() + ", OrderID: " + shortOrder.getId());
            return shortOrder.getId();
        }

        Long coinAmount = shortAppPurchaseGoods.getCoin() != null ? shortAppPurchaseGoods.getCoin().longValue() : 0L;
        Long originalCoin = shortUser.getCoin() != null ? shortUser.getCoin() : 0L;
        shortUser.setCoin(originalCoin + coinAmount);
        if (shortUser.getVipId() == null || (shortAppPurchaseGoods.getVipId() != null && !shortAppPurchaseGoods.getVipId().equals(shortUser.getVipId()))) {
            shortUser.setVipId(shortAppPurchaseGoods.getVipId());
        }
        shortUserMapper.updateShortUser(shortUser);

        ShortCoinRecord coinRecord = new ShortCoinRecord();
        coinRecord.setUserId(shortUser.getId());
        coinRecord.setAppId(shortOrder.getAppId());
        coinRecord.setCoin(coinAmount);
        coinRecord.setBalance(shortUser.getCoin());
        coinRecord.setConstype("1");
        coinRecord.setStatus("0");
        coinRecord.setCreateTime(new Date());
        shortCoinRecordService.insertShortCoinRecord(coinRecord);

        ShortRunlog runlog = new ShortRunlog();
        runlog.setType("APPLE_IAP_SUCCESS");
        runlog.setState("1");
        runlog.setContent(String.format("苹果内购成功 - 订单ID: %d, 用户ID: %d, 金币: %d, TransactionID: %s",
                shortOrder.getId(), shortUser.getId(), coinAmount, verifyResult.getTransactionId()));
        runlog.setCreateTime(new Date());
        shortRunlogService.insertShortRunlog(runlog);

        logger.info("苹果内购处理完成 - 订单[{}], 用户[{}], 充值金币[{}], 余额[{}], TransactionID: {}",
                shortOrder.getId(), shortUser.getId(), coinAmount, shortUser.getCoin(), verifyResult.getTransactionId());
        return shortOrder.getId();
    }


    @Override
    @Transactional
    public Boolean verifyGoogleReceipt(ReceiptRequest request) {
        /*logger.info("开始验证谷歌内购收据，商品ID: {}, 订单ID: {}", request.getId(), request.getOrderId());

        Long id = request.getId();
        ShortAppPurchaseGoods shortAppPurchaseGoods = appPurchaseGoodsMapper.selectById(id);
        if (shortAppPurchaseGoods == null) {
            logger.error("商品[{}]不存在", id);
            return false;
        }

        // 获取订单信息
        ShortOrder shortOrder = shortOrderMapper.selectShortOrderById(request.getOrderId());
        if (shortOrder == null) {
            logger.error("订单[{}]不存在", request.getOrderId());
            return false;
        }*/

        // todo 解决报错，稍后优化
        ShortOrder shortOrder = new ShortOrder();
        ShortAppPurchaseGoods shortAppPurchaseGoods = new ShortAppPurchaseGoods();
        Long id = 1L;

        // 检查订单是否已经处理过，防止重复处理
        if ("SUCCEEDED".equals(shortOrder.getStatus())) {
            logger.warn("订单[{}]已经处理过，跳过重复处理", shortOrder.getId());
            return true;
        }

        String packageName = shortAppPurchaseGoods.getPackageName();// 应用包名
        if (StringUtils.isEmpty(packageName)) {
            logger.error("商品[{}]包名为空", id);
            return false;
        }
        String productId = shortAppPurchaseGoods.getProductId();
        if (StringUtils.isEmpty(productId)) {
            logger.error("商品[{}]产品ID为空", id);
            return false;
        }
        String purchaseToken = request.getReceiptData(); // 购买 Token

        // 从配置中获取服务账号路径，如果没有配置则使用默认路径
        String serviceAccountPath = System.getProperty("google.service.account.path",
                "classpath:google-service-account.json");

        try {
            // 记录验证开始日志
            ShortRunlog startLog = new ShortRunlog();
            startLog.setType("GOOGLE_IAP_VERIFY_START");
            startLog.setState("1");
            startLog.setContent(String.format("开始验证谷歌内购 - 订单ID: %d, 包名: %s, 产品ID: %s",
                    shortOrder.getId(), packageName, productId));
            startLog.setCreateTime(new Date());
            shortRunlogService.insertShortRunlog(startLog);

            // 使用新版本的 GoogleCredentials 加载服务账户密钥
            GoogleCredentials credentials;
            if (serviceAccountPath.startsWith("classpath:")) {
                // 从classpath加载
                String resourcePath = serviceAccountPath.substring("classpath:".length());
                credentials = GoogleCredentials.fromStream(
                                this.getClass().getClassLoader().getResourceAsStream(resourcePath))
                        .createScoped(Collections.singletonList("https://www.googleapis.com/auth/androidpublisher"));
            } else {
                // 从文件系统加载
                credentials = GoogleCredentials.fromStream(Files.newInputStream(Paths.get(serviceAccountPath)))
                        .createScoped(Collections.singletonList("https://www.googleapis.com/auth/androidpublisher"));
            }

            // 构建 HTTP 客户端并添加认证头
            OkHttpClient client = new OkHttpClient.Builder()
                    .connectTimeout(30, TimeUnit.SECONDS)
                    .readTimeout(30, TimeUnit.SECONDS)
                    .build();
            Request httpRequest = new Request.Builder()
                    .url(String.format("https://androidpublisher.googleapis.com/androidpublisher/v3/applications/%s/purchases/products/%s/tokens/%s",
                            packageName, productId, purchaseToken))
                    .addHeader("Authorization", "Bearer " + credentials.refreshAccessToken().getTokenValue())
                    .build();

            Response response = client.newCall(httpRequest).execute();
            if (response.isSuccessful() && response.body() != null) {
                String responseBody = response.body().string();
                logger.info("谷歌内购验证响应: {}", responseBody);

                JSONObject json = JSONObject.parseObject(responseBody);
                Integer purchaseState = json.getInteger("purchaseState");

                // 保存验证结果
                saveGoogleVerifyResult(request, responseBody, shortOrder, shortAppPurchaseGoods);

                if (purchaseState != null && purchaseState == 0) { // 0 表示有效购买
                    return updateGoogleOrderAndUser(true, shortOrder, shortAppPurchaseGoods);
                } else {
                    logger.error("谷歌内购验证失败，购买状态: {}", purchaseState);
                    return updateGoogleOrderAndUser(false, shortOrder, shortAppPurchaseGoods);
                }
            } else {
                logger.error("谷歌内购验证失败，状态码: {}, 响应: {}",
                        response.code(), response.body() != null ? response.body().string() : "无响应体");
                return updateGoogleOrderAndUser(false, shortOrder, shortAppPurchaseGoods);
            }

        } catch (Exception e) {
            logger.error("谷歌内购验证异常", e);

            // 记录异常日志
            ShortRunlog errorLog = new ShortRunlog();
            errorLog.setType("GOOGLE_IAP_ERROR");
            errorLog.setState("0");
            errorLog.setContent(String.format("谷歌内购验证异常 - 订单ID: %d, 错误: %s",
                    shortOrder.getId(), e.getMessage()));
            errorLog.setCreateTime(new Date());
            shortRunlogService.insertShortRunlog(errorLog);

            return false;
        }
    }

    /**
     * 保存谷歌内购验证结果
     */
    private void saveGoogleVerifyResult(ReceiptRequest request, String responseBody,
                                        ShortOrder shortOrder, ShortAppPurchaseGoods shortAppPurchaseGoods) {
        try {
            // 判断商品的vip id，是否与订单vip id一致，防止骗单
            if (!Objects.equals(shortOrder.getVipId(), shortAppPurchaseGoods.getVipId())) {
                throw new RuntimeException("商品VIP ID与订单VIP ID不一致");
            }

            ShortAppPurchaseVerifyResult response = new ShortAppPurchaseVerifyResult();
//            response.setOrderId(request.getOrderId());
            response.setAppType("GOOGLE");
            response.setJsonObject(responseBody);

            // 从商品信息中获取应用ID
            response.setAppId(shortAppPurchaseGoods.getAppId());

            // 保存验证结果
            appPurchaseVerifyResultMapper.insert(response);

//            logger.info("谷歌内购验证结果已保存 - 订单ID: {}", request.getOrderId());
        } catch (Exception e) {
            logger.error("保存谷歌内购验证结果失败", e);
        }
    }

    /**
     * 更新谷歌内购订单和用户信息
     */
    private Boolean updateGoogleOrderAndUser(Boolean isSuccess, ShortOrder shortOrder,
                                             ShortAppPurchaseGoods shortAppPurchaseGoods) {
        if (isSuccess) {
            logger.info("谷歌内购验证成功，开始更新订单和用户信息");

            // 修改订单状态
            shortOrder.setPayTime(DateUtils.getNowDate());
            shortOrder.setStatus(OrderStatus.SUCCEEDED.getCode());
            shortOrderMapper.updateShortOrder(shortOrder);

            // 获取用户信息
            ShortUser shortUser = shortUserMapper.selectShortUserById(shortOrder.getUserId());
            if (shortUser == null) {
                logger.error("用户[{}]不存在", shortOrder.getUserId());
                return false;
            }

            // 更新用户金币
            Long coinAmount = shortAppPurchaseGoods.getCoin() != null ? shortAppPurchaseGoods.getCoin().longValue() : 0L;
            Long originalCoin = shortUser.getCoin() != null ? shortUser.getCoin() : 0L;
            shortUser.setCoin(originalCoin + coinAmount);
            shortUserMapper.updateShortUser(shortUser);

            // 记录金币变动日志
            ShortCoinRecord coinRecord = new ShortCoinRecord();
            coinRecord.setUserId(shortUser.getId());
            coinRecord.setAppId(shortOrder.getAppId());
            coinRecord.setCoin(coinAmount);
            coinRecord.setBalance(shortUser.getCoin());
            coinRecord.setConstype("1"); // 1:充值
            coinRecord.setStatus("0"); // 0:正常
            coinRecord.setCreateTime(new Date());
            shortCoinRecordService.insertShortCoinRecord(coinRecord);

            // 记录运行日志
            ShortRunlog runlog = new ShortRunlog();
            runlog.setType("GOOGLE_IAP_SUCCESS");
            runlog.setState("1");
            runlog.setContent(String.format("谷歌内购成功 - 订单ID: %d, 用户ID: %d, 金币: %d",
                    shortOrder.getId(), shortUser.getId(), coinAmount));
            runlog.setCreateTime(new Date());
            shortRunlogService.insertShortRunlog(runlog);

            logger.info("谷歌内购处理完成 - 订单[{}], 用户[{}], 充值金币[{}], 余额[{}]",
                    shortOrder.getId(), shortUser.getId(), coinAmount, shortUser.getCoin());
            return true;

        } else {
            logger.info("谷歌内购验证失败");

            // 更新订单状态
            shortOrder.setPayTime(DateUtils.getNowDate());
            shortOrder.setStatus(OrderStatus.REQUIRES_PAYMENT_METHOD.getCode());
            shortOrderMapper.updateShortOrder(shortOrder);

            // 记录失败日志
            ShortRunlog runlog = new ShortRunlog();
            runlog.setType("GOOGLE_IAP_FAILED");
            runlog.setState("0");
            runlog.setContent(String.format("谷歌内购验证失败 - 订单ID: %d", shortOrder.getId()));
            runlog.setCreateTime(new Date());
            shortRunlogService.insertShortRunlog(runlog);

            logger.error("谷歌内购验证失败 - 订单[{}]", shortOrder.getId());
            return false;
        }
    }

    /**
     * 创建内购订单
     *
     * @param appId   应用ID
     * @param goodsId 商品ID
     * @param token   用户token
     * @return 创建结果
     */
    @Override
    @Transactional
    public AjaxResult createOrder(Long appId, Long goodsId, String token, String userId) {
        logger.info("创建内购订单, 应用ID: {}, 商品ID: {}, 用户ID: {}", appId, goodsId, userId);

        try {
            // 1. 验证参数
            if (appId == null || goodsId == null || StringUtils.isEmpty(userId)) {
                return AjaxResult.error("参数错误");
            }

            // 2. 获取用户信息
            ShortUser user = null;

            // 如果有token，优先通过token获取用户
            if (StringUtils.isNotEmpty(token)) {
                ShortUser queryUser = new ShortUser();
                queryUser.setToken(token);
                List<ShortUser> users = shortUserMapper.selectShortUserList(queryUser);
                if (!users.isEmpty()) {
                    user = users.get(0);
                }
            }

            // 如果通过token没有找到用户，且提供了userId，则通过userId查找
            if (user == null && StringUtils.isNotEmpty(userId)) {
                try {
                    // 处理userId前缀
                    if (userId != null && userId.length() > 4 && userId.matches("\\d+")) {
                        userId = userId.substring(4);
                    }
                    Long userIdLong = Long.valueOf(userId);
                    user = shortUserMapper.selectShortUserById(userIdLong);
                } catch (NumberFormatException e) {
                    logger.error("用户ID格式错误: {}", userId);
                }
            }

            // 3. 获取商品信息
            ShortAppPurchaseGoods goods = appPurchaseGoodsMapper.selectById(goodsId);
            if (goods == null) {
                return AjaxResult.error("商品不存在");
            }

            // 4. 检查应用是否匹配
            if (!goods.getAppId().equals(appId)) {
                return AjaxResult.error("商品不属于该应用");
            }

            // 5. 检查是否存在未支付的相同订单
            ShortOrder queryOrder = new ShortOrder();
            if (user != null) {
                queryOrder.setUserId(user.getId());
            }
            queryOrder.setStatus(OrderStatus.PENDING.getCode());
            queryOrder.setOther("%\"productId\":\"" + goods.getProductId() + "\"%");
            List<ShortOrder> existingOrders = shortOrderMapper.selectListLikeProductId(queryOrder);

            if (existingOrders != null && !existingOrders.isEmpty()) {
                ShortOrder existingOrder = existingOrders.get(0);
                logger.info("已存在未支付订单，返回已有订单信息, 订单ID: {}", existingOrder.getId());

                // 构建返回数据
                JSONObject data = new JSONObject();
                data.put("orderId", existingOrder.getId());
                data.put("orderSn", existingOrder.getOrdersn());
                data.put("productId", goods.getProductId());
                data.put("price", goods.getPrice());
                data.put("currency", goods.getCurrency());
                data.put("coin", goods.getCoin());

                return AjaxResult.success("已存在未支付订单", data);
            }

            // 6. 创建订单
            ShortOrder order = new ShortOrder();
            order.setAppId(appId);
            order.setUserId(user.getId());
            order.setVipId(goods.getVipId());
            order.setPayType("充值"); // 默认为充值类型

            // 生成订单号 - 修改为使用"IAP_"前缀加随机码
            String orderSn = "IAP_" + paymentApiService.generateRandomCode();
            order.setOrdersn(orderSn);

            // 设置支付相关信息
            order.setPaymentCurrency(goods.getCurrency());
            order.setPaymentAmount(goods.getPrice());
            order.setAmount(goods.getPrice());
            order.setCurrency(goods.getCurrency());
            order.setStatus(OrderStatus.PENDING.getCode()); // 待支付状态

            // 设置支付方式
            order.setPaymentMethod("Apple".equalsIgnoreCase(goods.getAppType()) ? "APPLE_IAP" : "GOOGLE_IAP");

            // 设置时间字段
            Date now = new Date();
            order.setCreatedAt(now);
            order.setUpdatedAt(now);
            order.setCreateTime(now);
            order.setUpdateTime(now);

            // 设置推送状态
            order.setPushState("0"); // 初始状态为0，表示未推送

            // 设置其他字段
            JSONObject otherData = new JSONObject();
            otherData.put("goodsId", goods.getId());
            otherData.put("coin", goods.getCoin());
            otherData.put("productId", goods.getProductId());
            otherData.put("appType", goods.getAppType());
            order.setOther(otherData.toJSONString());

            // 保存订单
            shortOrderService.insertShortOrder(order);

            // 7. 构建返回数据
            JSONObject data = new JSONObject();
            data.put("orderId", order.getId());
            data.put("orderSn", orderSn);
            data.put("productId", goods.getProductId());
            data.put("price", goods.getPrice());
            data.put("currency", goods.getCurrency());
            data.put("coin", goods.getCoin());

            return AjaxResult.success("创建订单成功", data);

        } catch (Exception e) {
            logger.error("创建内购订单失败: {}", e.getMessage(), e);
            return AjaxResult.error("创建订单失败: " + e.getMessage());
        }
    }

    /**
     * 获取订单状态
     *
     * @param orderId 订单ID
     * @return 订单状态
     */
    @Override
    public AjaxResult getOrderStatus(Long orderId) {
        logger.info("获取订单状态, 订单ID: {}", orderId);

        try {
            // 查询订单
            ShortOrder order = shortOrderMapper.selectShortOrderById(orderId);
            if (order == null) {
                return AjaxResult.error("订单不存在");
            }

            // 构建返回数据
            JSONObject data = new JSONObject();
            data.put("orderId", order.getId());
            data.put("orderSn", order.getOrdersn());
            data.put("status", order.getStatus());
            data.put("payTime", order.getPayTime());
            data.put("amount", order.getAmount());
            data.put("currency", order.getCurrency());

            // 解析other字段获取金币数量
            if (StringUtils.isNotEmpty(order.getOther())) {
                JSONObject otherData = JSONObject.parseObject(order.getOther());
                if (otherData != null && otherData.containsKey("coin")) {
                    data.put("coin", otherData.getLong("coin"));
                }
            }

            return AjaxResult.success("获取订单状态成功", data);

        } catch (Exception e) {
            logger.error("获取订单状态失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取订单状态失败: " + e.getMessage());
        }
    }

    /**
     * 处理苹果内购通知回调
     *
     * @param notificationData 苹果服务器通知数据
     * @return 处理结果
     */
    @Override
    @Transactional
    public AjaxResult handleAppleNotification(String notificationData) {
        logger.info("处理苹果内购通知回调");

        try {
            // 记录原始通知数据
            ShortRunlog runlog = new ShortRunlog();
            runlog.setType("APPLE_IAP_NOTIFICATION");
            runlog.setState("1");
            runlog.setContent(notificationData);
            runlog.setCreateTime(new Date());
            shortRunlogService.insertShortRunlog(runlog);

            // 解析通知数据
            JSONObject notification = JSONObject.parseObject(notificationData);
            if (notification == null) {
                logger.error("解析苹果通知数据失败");
                return AjaxResult.error("解析通知数据失败");
            }

            // 获取通知类型
            String notificationType = notification.getString("notification_type");
            if (StringUtils.isEmpty(notificationType)) {
                logger.error("通知类型为空");
                return AjaxResult.error("通知类型为空");
            }

            // 获取统一收据数据
            JSONObject unifiedReceipt = notification.getJSONObject("unified_receipt");
            if (unifiedReceipt == null) {
                logger.error("统一收据数据为空");
                return AjaxResult.error("统一收据数据为空");
            }

            // 获取最新的收据信息
            JSONArray latestReceiptInfo = unifiedReceipt.getJSONArray("latest_receipt_info");
            if (latestReceiptInfo == null || latestReceiptInfo.isEmpty()) {
                logger.error("最新收据信息为空");
                return AjaxResult.error("最新收据信息为空");
            }

            // 处理每个收据
            for (int i = 0; i < latestReceiptInfo.size(); i++) {
                JSONObject receiptInfo = latestReceiptInfo.getJSONObject(i);
                String productId = receiptInfo.getString("product_id");
                String transactionId = receiptInfo.getString("transaction_id");

                // 查找对应的商品
                ShortAppPurchaseGoods queryGoods = new ShortAppPurchaseGoods();
                queryGoods.setProductId(productId);
                queryGoods.setAppType("Apple");
                LambdaQueryWrapper<ShortAppPurchaseGoods> wrapper = Wrappers.lambdaQuery(ShortAppPurchaseGoods.class)
                        .eq(ShortAppPurchaseGoods::getProductId, productId)
                        .eq(ShortAppPurchaseGoods::getAppType, "Apple");
                List<ShortAppPurchaseGoods> goodsList = appPurchaseGoodsMapper.selectList(wrapper);

                if (goodsList == null || goodsList.isEmpty()) {
                    logger.error("未找到对应的商品, productId: {}", productId);
                    continue;
                }

                ShortAppPurchaseGoods goods = goodsList.get(0);

                // 查找是否已有对应的订单
                ShortOrder queryOrder = new ShortOrder();
                queryOrder.setOther("%\"transactionId\":\"" + transactionId + "\"%");
                List<ShortOrder> orders = shortOrderMapper.selectListLikeProductId(queryOrder);

                ShortOrder order;
                if (orders != null && !orders.isEmpty()) {
                    // 更新现有订单
                    order = orders.get(0);
                } else {
                    // 查找待支付的订单
                    queryOrder = new ShortOrder();
                    queryOrder.setOther("%\"productId\":\"" + productId + "\"%");
                    queryOrder.setStatus(OrderStatus.PENDING.getCode());
                    orders = shortOrderMapper.selectListLikeProductId(queryOrder);

                    if (orders == null || orders.isEmpty()) {
                        logger.error("未找到对应的待支付订单, productId: {}", productId);
                        continue;
                    }

                    order = orders.get(0);
                }

                // 更新订单状态
                order.setStatus(OrderStatus.SUCCEEDED.getCode());
                order.setPayTime(new Date());

                // 更新订单的交易ID
                JSONObject otherData = JSONObject.parseObject(order.getOther());
                if (otherData == null) {
                    otherData = new JSONObject();
                }
                otherData.put("transactionId", transactionId);
                order.setOther(otherData.toJSONString());

                shortOrderMapper.updateShortOrder(order);

                // 更新用户金币
                ShortUser user = shortUserMapper.selectShortUserById(order.getUserId());
                if (user != null) {
                    // 获取金币数量
                    Long coinAmount = goods.getCoin() != null ? goods.getCoin().longValue() : 0L;

                    // 更新用户金币
                    user.setCoin(user.getCoin() + coinAmount);
                    shortUserMapper.updateShortUser(user);

                    // 记录金币变动
                    ShortCoinRecord coinRecord = new ShortCoinRecord();
                    coinRecord.setUserId(user.getId());
                    coinRecord.setAppId(order.getAppId());
                    coinRecord.setCoin(coinAmount);
                    coinRecord.setBalance(user.getCoin());
                    coinRecord.setConstype("1"); // 1:充值
                    coinRecord.setStatus("0"); // 0:正常
                    coinRecord.setCreateTime(new Date());
                    shortCoinRecordService.insertShortCoinRecord(coinRecord);
                }
            }

            return AjaxResult.success("处理苹果内购通知成功");

        } catch (Exception e) {
            logger.error("处理苹果内购通知失败: {}", e.getMessage(), e);
            return AjaxResult.error("处理苹果内购通知失败: " + e.getMessage());
        }
    }

    /**
     * 处理谷歌内购通知回调
     *
     * @param notificationData 谷歌服务器通知数据
     * @return 处理结果
     */
    @Override
    @Transactional
    public AjaxResult handleGoogleNotification(String notificationData) {
        logger.info("处理谷歌内购通知回调");

        try {
            // 记录原始通知数据
            ShortRunlog runlog = new ShortRunlog();
            runlog.setType("GOOGLE_IAP_NOTIFICATION");
            runlog.setState("1");
            runlog.setContent(notificationData);
            runlog.setCreateTime(new Date());
            shortRunlogService.insertShortRunlog(runlog);

            // 解析通知数据
            JSONObject notification = JSONObject.parseObject(notificationData);
            if (notification == null) {
                logger.error("解析谷歌通知数据失败");
                return AjaxResult.error("解析通知数据失败");
            }

            // 获取通知类型
            String notificationType = notification.getString("notificationType");
            if (StringUtils.isEmpty(notificationType)) {
                logger.error("通知类型为空");
                return AjaxResult.error("通知类型为空");
            }

            // 获取购买信息
            JSONObject purchaseInfo = notification.getJSONObject("purchaseInfo");
            if (purchaseInfo == null) {
                logger.error("购买信息为空");
                return AjaxResult.error("购买信息为空");
            }

            String productId = purchaseInfo.getString("productId");
            String purchaseToken = purchaseInfo.getString("purchaseToken");
            String orderId = purchaseInfo.getString("orderId");

            // 查找对应的商品
            ShortAppPurchaseGoods queryGoods = new ShortAppPurchaseGoods();
            queryGoods.setProductId(productId);
            queryGoods.setAppType("Google");
            LambdaQueryWrapper<ShortAppPurchaseGoods> wrapper = Wrappers.lambdaQuery(ShortAppPurchaseGoods.class)
                    .eq(ShortAppPurchaseGoods::getProductId, productId)
                    .eq(ShortAppPurchaseGoods::getAppType, "Google");
            List<ShortAppPurchaseGoods> goodsList = appPurchaseGoodsMapper.selectList(wrapper);

            if (goodsList == null || goodsList.isEmpty()) {
                logger.error("未找到对应的商品, productId: {}", productId);
                return AjaxResult.error("未找到对应的商品");
            }

            ShortAppPurchaseGoods goods = goodsList.get(0);

            // 查找是否已有对应的订单
            ShortOrder queryOrder = new ShortOrder();
            queryOrder.setOther("%\"purchaseToken\":\"" + purchaseToken + "\"%");
            List<ShortOrder> orders = shortOrderMapper.selectShortOrderList(queryOrder);

            ShortOrder order;
            if (orders != null && !orders.isEmpty()) {
                // 更新现有订单
                order = orders.get(0);
            } else {
                // 查找待支付的订单
                queryOrder = new ShortOrder();
                queryOrder.setOther("%\"productId\":\"" + productId + "\"%");
                queryOrder.setStatus(OrderStatus.PENDING.getCode());
                orders = shortOrderMapper.selectShortOrderList(queryOrder);

                if (orders == null || orders.isEmpty()) {
                    logger.error("未找到对应的待支付订单, productId: {}", productId);
                    return AjaxResult.error("未找到对应的待支付订单");
                }

                order = orders.get(0);
            }

            // 更新订单状态
            order.setStatus(OrderStatus.SUCCEEDED.getCode());
            order.setPayTime(new Date());

            // 更新订单的交易信息
            JSONObject otherData = JSONObject.parseObject(order.getOther());
            if (otherData == null) {
                otherData = new JSONObject();
            }
            otherData.put("purchaseToken", purchaseToken);
            otherData.put("googleOrderId", orderId);
            order.setOther(otherData.toJSONString());

            shortOrderMapper.updateShortOrder(order);

            // 更新用户金币
            ShortUser user = shortUserMapper.selectShortUserById(order.getUserId());
            if (user != null) {
                // 获取金币数量
                Long coinAmount = goods.getCoin() != null ? goods.getCoin().longValue() : 0L;

                // 更新用户金币
                user.setCoin(user.getCoin() + coinAmount);
                shortUserMapper.updateShortUser(user);

                // 记录金币变动
                ShortCoinRecord coinRecord = new ShortCoinRecord();
                coinRecord.setUserId(user.getId());
                coinRecord.setAppId(order.getAppId());
                coinRecord.setCoin(coinAmount);
                coinRecord.setBalance(user.getCoin());
                coinRecord.setConstype("1"); // 1:充值
                coinRecord.setStatus("0"); // 0:正常
                coinRecord.setCreateTime(new Date());
                shortCoinRecordService.insertShortCoinRecord(coinRecord);
            }

            return AjaxResult.success("处理谷歌内购通知成功");

        } catch (Exception e) {
            logger.error("处理谷歌内购通知失败: {}", e.getMessage(), e);
            return AjaxResult.error("处理谷歌内购通知失败: " + e.getMessage());
        }
    }
}
