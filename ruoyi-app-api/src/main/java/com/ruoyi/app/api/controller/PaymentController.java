package com.ruoyi.app.api.controller;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.app.api.dto.PaymentDisputeDTO;
import java.util.Map;
import java.util.List;

import javax.servlet.http.HttpServletRequest;

import com.ruoyi.app.api.dto.PaymentDisputeDTO;
import com.ruoyi.app.api.service.impl.AirwallexDisputeService;
import com.ruoyi.domain.ShortRunlog;
import com.ruoyi.service.IShortRunlogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.app.api.service.PaymentApiService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.StringUtils;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import io.swagger.annotations.ApiParam;

import com.alibaba.fastjson2.JSON;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 支付相关API控制器
 */
@Api(tags = "支付相关API")
@RestController
@RequestMapping("/api")
public class PaymentController {
    
    private static final Logger log = LoggerFactory.getLogger(PaymentController.class);

    @Autowired
    private PaymentApiService paymentApiService;

    @Autowired
    private AirwallexDisputeService airwallexDisputeService;

    /**
     * 创建意向支付
     */
    @ApiOperation(value = "创建意向支付", notes = "获取ext_client_id，用于支付处理")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "nid", value = "app_id", paramType = "header", required = true),
        @ApiImplicitParam(name = "emailFlag", value = "邮件营销活动标识", paramType = "header", required = false),
        @ApiImplicitParam(name = "user_id", value = "用户ID", paramType = "query", required = false),
        @ApiImplicitParam(name = "amount_id", value = "金额ID", paramType = "query", required = false)
    })
    @PostMapping("/create_intent/")
    public AjaxResult createIntent(
            @ApiParam(value = "支付创建请求参数", required = false, example = 
                "{\n" +
                "  \"user_id\": \"temp1234\",\n" +
                "  \"amount_id\": \"1\"\n" +
                "}"
            )
            @RequestBody(required = false) Map<String, Object> requestBody,
            @RequestParam(value = "user_id", required = false) String userIdParam,
            @RequestParam(value = "amount_id", required = false) String amountIdParam,
            HttpServletRequest request) {
        
        // 获取AppId
        String appId = request.getHeader("nid");
        
        // 同时支持URL参数和请求体参数，优先使用有值的参数
        String userId = null;
        String amountId = null;
        
        // 从请求体获取参数
        if (requestBody != null) {
            if (requestBody.containsKey("user_id")) {
                userId = (String) requestBody.get("user_id");
            }
            if (requestBody.containsKey("amount_id")) {
                amountId = (String) requestBody.get("amount_id");
            }
        }
        
        // 如果URL参数有值，优先使用URL参数
        if (StringUtils.isNotEmpty(userIdParam)) {
            userId = userIdParam;
        }
        if (StringUtils.isNotEmpty(amountIdParam)) {
            amountId = amountIdParam;
        }
        
        // 调用服务创建支付意图，传递request参数
        Map<String, Object> result = paymentApiService.createIntent(userId, amountId, appId, request);
        
        // 处理返回结果
        Integer code = (Integer) result.get("code");
        String msg = (String) result.get("msg");
        
        if (code == 200 && result.containsKey("data")) {
            Object data = result.get("data");
            return AjaxResult.success(msg, data);
        }
        
        return AjaxResult.error(code, msg);
    }
    
    /**
     * 获取支付状态
     */
    @ApiOperation(value = "获取支付状态", notes = "通过订单ID获取支付状态")
    @PostMapping("/get_order_stauts/")
    public AjaxResult getOrderStatus(@RequestBody Map<String, String> orderInfo) {
        // 获取商户订单ID
        String merchantOrderId = orderInfo.get("merchant_order_id");
        
        if (merchantOrderId == null || merchantOrderId.isEmpty()) {
            return AjaxResult.error(400, "Invalid JSON data");
        }
        
        // 调用服务获取订单状态
        Map<String, Object> result = paymentApiService.getOrderStatus(merchantOrderId);
        
        // 处理返回结果
        Integer code = (Integer) result.get("code");
        
        if (code == 200 && result.containsKey("data")) {
            @SuppressWarnings("unchecked")
            Map<String, Object> data = (Map<String, Object>) result.get("data");
            return AjaxResult.success("获取支付状态成功", data);
        }
        
        String msg = (String) result.get("msg");
        return AjaxResult.error(code, msg);
    }
    
    /**
     * 支付意图回调
     */
    @ApiOperation(value = "支付意图回调", notes = "处理支付平台的回调请求，包括支付完成和争议事件")
    @PostMapping("/v1/pa/payment_intents/callback")
    public AjaxResult paymentIntentsCallback(@RequestBody Map<String, Object> callbackData) {
        try {
//            // 判断是否为争议事件
//            if (callbackData != null && callbackData.containsKey("name")
//                    && callbackData.get("name") instanceof String
//                    && ((String)callbackData.get("name")).startsWith("payment_dispute.")) {
//
//                log.info("接收到争议事件回调: {}", callbackData.get("name"));
//
//                // 转换为争议DTO处理
//                PaymentDisputeDTO disputeDTO = JSON.parseObject(JSON.toJSONString(callbackData), PaymentDisputeDTO.class);
//                return airwallexDisputeService.handleDisputeCallback(disputeDTO);
//            }

            // 处理正常支付回调
            Map<String, Object> result = paymentApiService.handlePaymentCallback(callbackData);

            // 处理返回结果
            Integer code = (Integer) result.get("code");

            if (code == 200 && result.containsKey("data")) {
                @SuppressWarnings("unchecked")
                Map<String, Object> data = (Map<String, Object>) result.get("data");
                return AjaxResult.success("处理回调成功", data);
            }

            String msg = (String) result.get("msg");
            return AjaxResult.error(code, msg);
        } catch (Exception e) {
            log.error("处理回调异常: {}", e.getMessage(), e);
            return AjaxResult.error("处理回调异常: " + e.getMessage());
        }
    }
    
    /**
     * 订阅支付回调
     */
    @ApiOperation(value = "订阅支付回调", notes = "处理订阅支付的回调请求")
    @PostMapping("/v1/pa/payment_intents/subscribe")
    public AjaxResult paymentIntentsSubscribe(@RequestBody Map<String, Object> subscribeData) {
        // 调用服务处理订阅回调
        Map<String, Object> result = paymentApiService.handleSubscribeCallback(subscribeData);
        
        // 处理返回结果
        Integer code = (Integer) result.get("code");
        String msg = (String) result.get("msg");
        
        if (code == 200) {
            return AjaxResult.success(msg);
        }
        
        return AjaxResult.error(code, msg);
    }
    
    /**
     * 获取VIP类型数据
     */
    @ApiOperation(value = "获取VIP类型数据", notes = "获取指定充值模板的VIP类型数据")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "tid", value = "充值模板ID", paramType = "header", required = true)
    })
    @GetMapping("/get_vip_list/")
    public AjaxResult getVipList(HttpServletRequest request) {
        // 获取充值模板ID
        String templateId = request.getHeader("tid");
        String userId = request.getHeader("userId")  ==null ? "-1" : request.getHeader("userId");
        String emailFlag = request.getHeader("emailFlag") ==null ? "false" : request.getHeader("emailFlag");

        String linkAppId = request.getHeader("linkAppId");

        // 调用服务获取VIP列表
        Map<String, Object> result = paymentApiService.getVipList(templateId,Boolean.parseBoolean(emailFlag),userId,linkAppId);
        
        // 处理返回结果
        Integer code = (Integer) result.get("code");
        
        if (code == 200 && result.containsKey("data")) {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> data = (List<Map<String, Object>>) result.get("data");
            return AjaxResult.success("success", data);
        }
        
        String msg = (String) result.get("msg");
        return AjaxResult.error(code, msg);
    }
    
    /**
     * 获取金币消费记录
     */
    @ApiOperation(value = "获取金币消费记录", notes = "获取用户的所有金币消费记录")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "Authorization", value = "Authorization", paramType = "header", required = false),
        @ApiImplicitParam(name = "uid", value = "用户ID", paramType = "query", required = false)
    })
    @GetMapping("/coin-records/")
    public AjaxResult getCoinRecords(HttpServletRequest request, @RequestParam(value = "uid", required = false) String uid) {
        // 获取token或uid
        String token = request.getHeader("Authorization");
        String uidHeader = request.getHeader("uid");
        
        // 如果请求参数提供了uid，优先使用
        if (uid == null || uid.isEmpty()) {
            uid = uidHeader;
        }
        
        // 调用服务获取金币记录
        Map<String, Object> result = paymentApiService.getCoinRecords(token, uid);
        
        // 处理返回结果
        Integer code = (Integer) result.get("code");
        
        if (code == 200 && result.containsKey("data")) {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> data = (List<Map<String, Object>>) result.get("data");
            return AjaxResult.success(data);
        }
        
        String msg = (String) result.get("msg");
        return AjaxResult.error(code, msg);
    }
    
    /**
     * 扣除用户金币
     */
    @ApiOperation(value = "扣除用户金币", notes = "从用户账户扣除金币，用于解锁视频内容")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "Authorization", value = "Authorization", paramType = "header", required = false, dataType = "String"),
        @ApiImplicitParam(name = "nid", value = "app_id", paramType = "header", required = true, dataType = "Integer"),
        @ApiImplicitParam(name = "uid", value = "uid,临时用户id", paramType = "header", required = false, dataType = "String")
    })
    @ApiResponses({
        @ApiResponse(code = 200, message = "扣除成功", response = Map.class),
        @ApiResponse(code = 403, message = "金币不足"),
        @ApiResponse(code = 404, message = "用户不存在"),
        @ApiResponse(code = 400, message = "无效的JSON数据或参数"),
        @ApiResponse(code = 500, message = "内部服务器错误")
    })
    @PostMapping("/deduct_user_coin/")
    public AjaxResult deductUserCoin(HttpServletRequest request, 
            @ApiParam(value = "扣除金币请求参数", required = true, example = 
                "{\n" +
                "  \"movie\": 1,\n" +
                "  \"video\": 1,\n" +
                "  \"constype\": \"unlock_video\",\n" +
                "  \"unlock_type\": \"0\",\n" +
                "  \"kid\": \"1\"\n" +
                "}"
            ) 
            @RequestBody Map<String, Object> deductData) {
        // 获取请求头参数
        String token = request.getHeader("Authorization");
        String appId = request.getHeader("nid");
        String uid = request.getHeader("uid");
        
        Map<String, Object> result = paymentApiService.deductUserCoin(token, appId, uid, deductData);
        
        // 处理返回结果
        Integer code = (Integer) result.get("code");
        
        if (code == 200 && result.containsKey("data")) {
            @SuppressWarnings("unchecked")
            Map<String, Object> data = (Map<String, Object>) result.get("data");
            return AjaxResult.success((String) result.get("msg"), data);
        }
        
        String msg = (String) result.get("msg");
        if (result.containsKey("data")) {
            @SuppressWarnings("unchecked")
            Map<String, Object> data = (Map<String, Object>) result.get("data");
            return new AjaxResult(code, msg, data);
        }
        return AjaxResult.error(code, msg);
    }

    /**
     * 接受支付争议
     */
    @ApiOperation(value = "接受支付争议", notes = "接受支付争议并进行自动退款")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "disputeId", value = "争议ID", required = true, paramType = "path")
    })
    @PostMapping("/v1/pa/payment_disputes/{disputeId}/accept")
    public AjaxResult acceptDispute(@PathVariable("disputeId") String disputeId) {

        // 调用服务接受争议
        return paymentApiService.acceptDispute(disputeId);
    }

    /**
     * 挑战支付争议
     */
    @ApiOperation(value = "挑战支付争议", notes = "提供证据来挑战支付争议")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "disputeId", value = "争议ID", required = true, paramType = "path")
    })
    @PostMapping("/v1/pa/payment_disputes/{disputeId}/challenge")
    public AjaxResult challengeDispute(
            @PathVariable("disputeId") String disputeId,
            @ApiParam(value = "争议证据", required = true)
            @RequestBody Map<String, Object> evidence) {

        // 调用服务挑战争议
        return paymentApiService.challengeDispute(disputeId, evidence);
    }
}