
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta charset="utf-8" name="format-detection" content="telephone = no,viewport-fit=cover">
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no,viewport-fit=cover">
    <title>${appName}</title>
    <meta name="description" content="${movieName}" />
    <meta name="author" content="${appName}" />
    <meta name="keywords" content="${appName},${movieName}" />
    <style>
        html {
            font-size: 13.3333vw
        }

        html,
        html body {
            width: 100%;
            height: 100%
        }

        html body {
            font-size: .32rem
        }

        * {
            margin: 0;
            padding: 0;
            -webkit-box-sizing: border-box;
            box-sizing: border-box;
            -moz-user-select: text;
            -ms-user-select: text;
            user-select: text;
            -webkit-user-select: text;
            font-family: Rubik-Regular;
        }

        li {
            list-style: none
        }

        input,
        textarea {
            -webkit-user-select: auto;
            border: 0;
            outline: none;
            background-color: transparent
        }

        @font-face {
            font-family: Rubik-Regular;
            src: url("data:font/woff2;charset=utf-8;base64,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") format("woff2");
            font-weight: 400;
            font-style: normal;
            font-display: swap
        }


        .btn:active {
            opacity: .7
        }

        .disabled {
            opacity: .5
        }

        .name-3,
        .nowrap {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap
        }

        .name-3 {
            max-width: 3rem
        }

        .flex,
        .flex-column {
            display: -webkit-box;
            display: -webkit-flex;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-align: center;
            -webkit-align-items: center;
            -ms-flex-align: center;
            align-items: center
        }

        .flex-column {
            -webkit-box-orient: vertical;
            -webkit-box-direction: normal;
            -webkit-flex-direction: column;
            -ms-flex-direction: column;
            flex-direction: column
        }

        .flex-center,
        .flex-center-column,
        .flex-column-center {
            display: -webkit-box;
            display: -webkit-flex;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-align: center;
            -webkit-align-items: center;
            -ms-flex-align: center;
            align-items: center;
            -webkit-box-pack: center;
            -webkit-justify-content: center;
            -ms-flex-pack: center;
            justify-content: center
        }

        .flex-center-column,
        .flex-column-center {
            -webkit-box-orient: vertical;
            -webkit-box-direction: normal;
            -webkit-flex-direction: column;
            -ms-flex-direction: column;
            flex-direction: column
        }

        .flex-between {
            display: -webkit-box;
            display: -webkit-flex;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-align: center;
            -webkit-align-items: center;
            -ms-flex-align: center;
            align-items: center;
            -webkit-box-pack: justify;
            -webkit-justify-content: space-between;
            -ms-flex-pack: justify;
            justify-content: space-between
        }

        .red {
            color: #ff4141
        }

        body {
            background-color: #1c1c1d
        }

        .container {
            position: relative;
            width: 100%;
            height: 100vh;
            overflow-x: hidden
        }

        .nav-view {
            height: 1.3rem;
            background: #fff
        }

        .nav-view .nav-box {
            width: 100%;
            position: fixed;
            top: 0;
            z-index: 1001
        }

        .nav-view .nav {
            width: 100%;
            padding: .2rem .3rem;
            background: #fff
        }

        .nav-view .nav .logo {
            margin-right: .25rem;
            width: .9rem;
            height: .9rem;
            background-image: url(data:image/png;base64,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);
            background-size: 100% 100%
        }

        .nav-view .nav .nav-title {
            font-family: Rubik-SemiBold;
            font-size: .38rem;
            font-weight: 600;
            line-height: normal;
            letter-spacing: 0;
            font-variation-settings: "opsz" auto;
            color: #1c1c1d
        }

        .nav-view .nav .nav-remarks {
            font-size: .2rem;
            font-weight: 500;
            line-height: normal;
            letter-spacing: 0;
            font-variation-settings: "opsz" auto;
            color: #1c1c1d;
            zoom: .9
        }

        .nav-view .nav .nav-btn {
            color: #fff;
            font-size: .28rem;
            font-weight: 500;
            padding: .14rem .4rem;
            border-radius: 6.56rem;
            background: -webkit-gradient(linear, right top, left top, from(#544B95), color-stop(56%, #2D2758), to(#2D2758));
            background: linear-gradient(270deg, #544B95, #2D2758)
        }

        .van-image__error,
        .van-image__loading {
            background-color: transparent
        }

        .bg {
            position: absolute;
            top: 0;
            left: 0;
            height: 8.8rem;
            -webkit-filter: blur(.3rem);
            filter: blur(.3rem);
            -webkit-transform: scale(1.1);
            transform: scale(1.1);
            z-index: 1
        }

        .bg,
        .bg img {
            width: 100%;
            -o-object-fit: cover;
            object-fit: cover
        }

        .bg img {
            height: 100%
        }

        .main {
            position: relative;
            z-index: 2;
            color: #fff;
            text-align: center;
            padding-bottom: 2rem;
            min-height: -webkit-calc(100vh - 2rem);
            min-height: calc(100vh - 2rem);
            display: -webkit-box;
            display: -webkit-flex;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-orient: vertical;
            -webkit-box-direction: normal;
            -webkit-flex-direction: column;
            -ms-flex-direction: column;
            flex-direction: column
        }

        .head {
            padding: 1.17rem 0 .36rem 0
        }

        .logo {
            margin-right: .16rem;
            width: .64rem;
            height: .64rem;
            border-radius: .1463rem
        }

        .name {
            font-family: Rubik-SemiBold;
            font-size: .36rem;
            color: #fff;
            text-shadow: 0 0 .1rem rgba(0, 0, 0, .3)
        }

        .main-img-box {
            width: 100%;
            height: 9.95rem;
            position: relative
        }

        .icon-play {
            position: absolute;
            top: 4.28rem;
            left: 3.05rem;
            width: 1.2rem;
            height: 1.2rem;
            background-image: url('https://file.flareshort.com/images/tp1/play.png');
            background-size: 100% 100%;
            z-index: 2;
            -webkit-animation: pulse .5s infinite;
            animation: pulse .5s infinite
        }

        .img {
            margin: 0 auto;
            width: 100%;
            height: 9.95rem
        }

        .img img {
            width: 100%;
            height: 100%;
            -o-object-fit: cover;
            object-fit: cover
        }

        .title {
            text-align: left;
            padding: .3rem .32rem .1rem .32rem;
            font-family: Rubik-SemiBold;
            font-size: .5rem;
            font-weight: 600;
            line-height: normal;
            letter-spacing: 0;
            font-variation-settings: "opsz" auto;
            color: #fff
        }

        .content {
            -webkit-box-flex: 1;
            -webkit-flex: 1;
            -ms-flex: 1;
            flex: 1;
            padding: 0 .32rem;
            letter-spacing: 0;
            color: hsla(0, 0%, 100%, .5)
        }

        .content,
        .test-content {
            text-align: left;
            font-size: .28rem;
            font-weight: 400;
            line-height: .4rem
        }

        .test-content {
            padding: 0 .48rem;
            color: red;
            word-wrap: break-word
        }

        .btn {
            position: fixed;
            bottom: .73rem;
            left: .32rem;
            width: 6.86rem;
            height: 1rem;
            background: -webkit-gradient(linear, left top, right top, from(#7959F3), to(#9CC8F9));
            background: linear-gradient(90deg, #7959F3, #9CC8F9);
            font-size: .36rem;
            font-weight: 600;
            font-family: Rubik-SemiBold;
            border-radius: 3.96rem
        }

        .btn2 {
            background: -webkit-gradient(linear, left top, right top, from(#7959F3), to(#fe9109));
            background: linear-gradient(90deg, #7959F3, #fe9109)
        }

        .btn3 {
            background: -webkit-gradient(linear, left top, right top, from(#ff1469), to(#ffa925));
            background: linear-gradient(90deg, #ff1469, #ffa925)
        }

        .btn4 {
            background: -webkit-gradient(linear, left top, right top, from(#0c55ff), to(#17ebbb));
            background: linear-gradient(90deg, #0c55ff, #17ebbb)
        }

        .footer5 {
            position: fixed;
            bottom: 0;
            width: 100%;
            height: 2rem;
            background: #1c1c1d;
            padding: .24rem .32rem 0 .32rem
        }

        .footer5 .btn-view {
            position: relative
        }

        .footer5 .btn5-tips {
            padding: 0 0 .16rem 0;
            color: #dbaa88;
            font-weight: 400;
            font-size: .24rem
        }

        .footer5 .btn5-tips .act {
            font-weight: 600;
            color: #d03697
        }

        .footer5 .btn5 {
            width: 6.86rem;
            height: 1rem;
            font-size: .36rem;
            font-weight: 600;
            font-family: Rubik-SemiBold;
            border-radius: 3.96rem;
            background: -webkit-gradient(linear, right top, left top, from(#544B95), color-stop(56%, #2D2758), to(#2D2758));
            background: linear-gradient(270deg, #544B95, #2D2758)
        }

        @-webkit-keyframes pulse {
            0% {
                -webkit-transform: scale(1);
                transform: scale(1)
            }

            50% {
                -webkit-transform: scale(.9);
                transform: scale(.9)
            }

            to {
                -webkit-transform: scale(.8);
                transform: scale(.8)
            }
        }

        @keyframes pulse {
            0% {
                -webkit-transform: scale(1);
                transform: scale(1)
            }

            50% {
                -webkit-transform: scale(.9);
                transform: scale(.9)
            }

            to {
                -webkit-transform: scale(.8);
                transform: scale(.8)
            }
        }

        .btn-icon1 {
            position: absolute;
            top: .15rem;
            right: 0;
            width: 1.392rem;
            height: 1.348rem;
            background-size: 100% 100%;
            -webkit-animation: afterScale 1s linear infinite;
            animation: afterScale 1s linear infinite;
            background-image: url('https://file.flareshort.com/images/tp1/move-point.png')
        }

        @-webkit-keyframes afterScale {

            0%,
            to {
                top: .45rem;
                right: -.3rem
            }

            50% {
                top: .3rem;
                right: -.1rem
            }
        }

        @keyframes afterScale {

            0%,
            to {
                top: .45rem;
                right: -.3rem
            }

            50% {
                top: .3rem;
                right: -.1rem
            }
        }
    </style>
    <style>
        html {
            -webkit-tap-highlight-color: transparent
        }

        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, Helvetica Neue, Helvetica, Segoe UI, Arial, Roboto, PingFang SC, miui, Hiragino Sans GB, Microsoft Yahei, sans-serif
        }

        a {
            text-decoration: none
        }

        button,
        input,
        textarea {
            color: inherit;
            font: inherit
        }

        [class*=van-]:focus,
        a:focus,
        button:focus,
        input:focus,
        textarea:focus {
            outline: 0
        }

        ol,
        ul {
            margin: 0;
            padding: 0;
            list-style: none
        }
    </style>
    <script>
        // build a new URL with parameters p0-p6, fbclid and fbpaid
        function buildURL(p0, p1, p2, p3, p4, p5, p6, fbclid, fbpid) {
            if (!p0) {
                return null;
            }
            tracker_token = p0;
            if (p1 || p2) {
                campaign = p1 + "(" + p2 + ")";
            } else {
                campaign = "";
            }
            if (p3 || p4) {
                adgroup = p3 + "(" + p4 + ")";
            } else {
                adgroup = "";
            }
            if (p5 || p6) {
                creative = p5 + "(" + p6 + ")";
            } else {
                creative = "";
            }
            if (!fbclid) {
                fbclid = "";
            }
            if (!fbpid) {
                fbpid = "";
            }
            // build a tracker URL
            let params = {
                campaign,
                adgroup,
                creative,
                fbclid,
                fbpid
            };
            let newURL =
                "https://app.adjust.com/" +
                tracker_token +
                "?" +
                Object.keys(params)
                .map((key) => key + "=" + encodeURIComponent(params[key]))
                .join("&");
            return newURL;
        }
        //get fbpid from Cookie written by Facebook Pixel
        function getFbPid() {
            let fbPid = document.cookie.match(/(^|;) ?_fbp=([^;]*)(;|$)/);
            if (fbPid) {
                return fbPid[2];
            } else {
                return null;
            }
        }
    </script>
    <script>
        // get query string, and parse it with URLSearchParams
        const urlParams = new URLSearchParams(window.location.search);
        //p0 = urlParams.get("p0") and fallback to "default"
        const p0 = urlParams.get("p0");
        const p1 = urlParams.get("p1");
        const p2 = urlParams.get("p2");
        const p3 = urlParams.get("p3");
        const p4 = urlParams.get("p4");
        const p5 = urlParams.get("p5");
        const p6 = urlParams.get("p6");
        const fbPid = getFbPid();
        const fbClickId = urlParams.get("fbclid");
        const linkid = urlParams.get("linkid");
        const url = buildURL(p0, p1, p2, p3, p4, p5, p6, fbClickId, fbPid);

        const encodeDeepLinkUrl = encodeURIComponent("${deep_link}")
        // TODO:需要服务器端查询处理数据
        let data = {
            campaignName: p1,
            campaignId: p2,
            adsetName: p3,
            adsetId: p4,
            adName: p5,
            adId: p6,
            qd: "${qd}",  // 渠道
            movie: "${mid}",  // 电影ID
            pid: "${pid}",  // 支付面板费用ID
            kid: "${kid}",  // 金币扣费ID
            pixel_id: "${pixel_id}",  // 像素ID
            pageName: 'MovieDetail', // 路由名字 当前跳转的是短剧详情页面
            deep_link: "${deep_link}",  // 深度链接
            fbc: fbClickId,
            fbp: fbPid,
            ua: window.navigator.userAgent,
            linkid
        }

        const keys = Object.keys(data).filter(key => key !== 'deep_link');
        const paramsString = keys.reduce(function (pre, current) {
            return pre + current + `=` + data[current] + `&`
        }, '').slice(0, -1);
        const schemeUrl = encodeURIComponent(paramsString);

        // 回退方法：使用传统的 document.execCommand 方式
        function fallbackCopy(text) {
            // 创建一个临时的文本区域元素
            const textArea = document.createElement('textarea');
            textArea.value = text;

            // 设置样式使其不可见
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);

            // 选择文本并复制
            textArea.focus();
            textArea.select();

            let successful = false;
            try {
                successful = document.execCommand('copy');
                console.log(successful ? '内容已成功复制到剪贴板' : '复制失败');
            } catch (err) {
                console.error('复制失败:', err);
            }
            // 移除临时元素
            document.body.removeChild(textArea);
        }

        // TODO
        const baseUrl = 'https://app.flareshort.com';
        const uploadDeviceInfo = async (linkUrl) => {

            const platform = navigator.platform.includes('Linux') ? 'android' : navigator.platform.includes(
                'iPhone') ? 'ios' : 'pc';
            const screenWidth = screen.width;
            const screenHeight = screen.height;
            const systemTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;

            let response = await fetch(baseUrl + `/api/get_ip_info/`);
            let ipInfo = await response.json();
            if (ipInfo.code === 200) {
                if (!ipInfo.data?.ip) {
                    return;
                }
                const deviceInfo = {
                    platform: platform,
                    screenWidth: screenWidth,
                    screenHeight: screenHeight,
                    systemTimeZone: systemTimeZone,
                    ip: ipInfo.data.ip,
                    linkUrl: linkUrl,
                };
                const queryParams = new URLSearchParams(deviceInfo).toString();
                await fetch(baseUrl + `/api/store_device_info/?` + queryParams, {
                    method: 'GET',
                    headers: {
                        'NID': "${NID}"
                    }
                });
            }
        }
        uploadDeviceInfo("${af_link_url}" +
            "?af_xp=social&pid=Social_facebook&c=test-fb&deep_link_value=" + schemeUrl +
            "&deep_link_sub1=test_link_value_extra&af_dp=" + encodeDeepLinkUrl + "&af_click_lookback=7d"
        );
        // 添加点击事件处理函数
        document.addEventListener('DOMContentLoaded', function () {
            document.body.addEventListener('click', function () {
                const linkUrl = "${af_link_url}" +
                    "?af_xp=social&pid=Social_facebook&c=test-fb&deep_link_value=" +
                    schemeUrl +
                    "&deep_link_sub1=test_link_value_extra&af_dp=" + encodeDeepLinkUrl + "&af_click_lookback=7d"
                try {
                    if (navigator.clipboard && navigator.clipboard.writeText) {
                        navigator.clipboard.writeText(linkUrl)
                            .then(() => {})
                            .catch(err => {
                                // 如果 Clipboard API 失败，回退到方法2
                                fallbackCopy(linkUrl);
                            });
                    } else {
                        // 浏览器不支持 Clipboard API，使用回退方法
                        fallbackCopy(linkUrl);
                    }
                } catch (e) {
                    console.error(e);
                }

                // 需要把fb携带过来的参数带到adjust
                window.location.href = linkUrl
            });
        });
    </script>
    <!-- Meta Pixel Code -->
    <script>
        function getQueryParam(param) {
            const params = new URLSearchParams(window.location.search);
            return params.get(param);
        }
        // 检查当前域名是否为 share.flareshort.com
        ! function (f, b, e, v, n, t, s) {
            if (f.fbq) return;
            n = f.fbq = function () {
                n.callMethod ?
                    n.callMethod.apply(n, arguments) : n.queue.push(arguments)
            };
            if (!f._fbq) f._fbq = n;
            n.push = n;
            n.loaded = !0;
            n.version = '2.0';
            n.queue = [];
            t = b.createElement(e);
            t.async = !0;
            t.src = v;
            s = b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t, s)
        }(window, document, 'script',
            'https://connect.facebook.net/en_US/fbevents.js');
        fbq('init', '${pixel_id}');
        fbq('track', 'PageView');
    </script>
    <noscript><img height="1" width="1" style="display:none"
            src="https://www.facebook.com/tr?id=${pixel_id}&ev=PageView&noscript=1" /></noscript>
    <!-- End Meta Pixel Code -->
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-4K5VT8KBKR"></script>
    <script>
        window.dataLayer = window.dataLayer || [];

        function gtag() {
            dataLayer.push(arguments);
        }
        gtag('js', new Date());
        gtag('config', 'G-4K5VT8KBKR');
    </script>

<body>
    <h1 style="display: none">${appName}</h1>
    <div class="container">
        <div class="nav-view">
            <div class="nav-box">
                <div class="nav flex-between">
                    <div class="flex">
                        <div class="logo"></div>
                        <div>
                            <div class="nav-title">${appName}</div>
                            <div class="nav-remarks">The Hottest DramaNote Platform</div>
                        </div>
                    </div>
                    <div class="nav-btn">Download</div>
                </div>
            </div>
        </div>
        <div class="main">
            <div class="main-img-box">
                <div class="icon-play"></div>
                <div class="img van-image"><img class="van-image__img" alt="movie-icon" src="${movieIcon}"></div>
            </div>
            <div class="title">${movieName}</div>
            <div class="content">
                <div class="content-item">
                    <div class="item-text">🔥 Endless entertainment—swipe, watch, and enjoy!</div>
                </div>
                <div class="content-item">
                    <div class="item-text">💖Handpicked stories that keep you hooked!</div>
                </div>
                <div class="content-item">
                    <div class="item-text">🚀Download the app now for a seamless, high-quality viewing experience!</div>
                </div>
            </div>
            <div class="footer5">
                <div class="btn5-tips">Download the <span class="act">${appName}</span> App to continue watching.</div>
                <div class="btn-view">
                    <div class="btn-icon1"> </div>
                    <div class="btn5 flex-center">Continue Watching</div>
                </div>
            </div>
        </div>
    </div>
</body>
<div id="immersive-translate-popup" style="all: initial"></div>

</html>