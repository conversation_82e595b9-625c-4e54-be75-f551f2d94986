package com.ruoyi.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 三方支付平台信息对象 short_extplats
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
@Data
@TableName(autoResultMap = true)
public class ShortExtplats extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**  平台名称 */
    @Excel(name = " 平台名称")
    private String name;

    /** 平台token */
    @Excel(name = "平台token")
    private String token;

    /** 平台key */
    @Excel(name = "平台key")
    private String key;

    /** 平台client_id
 */
    @Excel(name = "平台client_id")
    private String clientId;

    /**  平台client_secret
 */
    @Excel(name = " 平台client_secret")
    private String clientSecret;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }

    public void setToken(String token) 
    {
        this.token = token;
    }

    public String getToken() 
    {
        return token;
    }

    public void setKey(String key) 
    {
        this.key = key;
    }

    public String getKey() 
    {
        return key;
    }

    public void setClientId(String clientId) 
    {
        this.clientId = clientId;
    }

    public String getClientId() 
    {
        return clientId;
    }

    public void setClientSecret(String clientSecret) 
    {
        this.clientSecret = clientSecret;
    }

    public String getClientSecret() 
    {
        return clientSecret;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("name", getName())
            .append("token", getToken())
            .append("key", getKey())
            .append("clientId", getClientId())
            .append("clientSecret", getClientSecret())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
