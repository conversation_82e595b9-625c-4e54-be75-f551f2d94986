package com.ruoyi.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 订单表对象 short_order
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
@Data
@ToString
@TableName(autoResultMap = true)
public class ShortOrder extends BaseEntity
{

    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 应用 */
    @Excel(name = "应用")
    private Long appId;

    /** 用户 */
    @Excel(name = "用户")
    private Long userId;

    /** vip */
    @Excel(name = "vip")
    private Long vipId;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long linkId;

    /** 广告ID 0410日新增
 */
    @Excel(name = "广告ID 0410日新增")
    private String adid;

    /** 类型 充值 or 订阅
 */
    @Excel(name = "类型 充值 or 订阅")
    private String payType;

    /** 订单编号 */
    @Excel(name = "订单编号")
    private String ordersn;

    /** 商户订单ID
 */
    @Excel(name = "商户订单ID")
    private String merchantOrderId;

    /** 支付意图ID */
    @Excel(name = "支付意图ID")
    private String paymentIntentId;

    /** 支付方式 */
    @Excel(name = "支付方式")
    private String paymentMethod;

    /** 请求ID
 */
    @Excel(name = "请求ID")
    private String requestId;

    /** 支付货币
 */
    @Excel(name = "支付货币")
    private String paymentCurrency;

    /** 支付金额 */
    @Excel(name = "支付金额")
    private BigDecimal paymentAmount;

    /** 费用货币 */
    @Excel(name = "费用货币")
    private String feeCurrency;

    /** 费用金额 */
    @Excel(name = "费用金额")
    private BigDecimal feeAmount;

    /** 金额 */
    @Excel(name = "金额")
    private BigDecimal amount;

    /** 货币 */
    @Excel(name = "货币")
    private String currency;

    /** 状态
 */
    @Excel(name = "状态")
    private String status;

    /** 客户端密钥 */
    @Excel(name = "客户端密钥")
    private String clientSecret;

    /** 原始数据
 */
    @Excel(name = "原始数据")
    private String rawData;

    /**  链接时间 0411日新增
 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = " 链接时间 0411日新增", width = 30, dateFormat = "yyyy-MM-dd")
    private Date linkTime;

    /** 其他信息 0411日新增
 */
    @Excel(name = "其他信息 0411日新增")
    private String other;

    /** 推送状态 0未推送 1已推送
 */
    @Excel(name = "推送状态 0未推送 1已推送")
    private String pushState;

    @Excel(name = "用户名称")
    private String userName;

    @Excel(name = "应用名称")
    private String appName;

    @Excel(name = "vipName")
    private String vipName;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String createdStartDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String createdEndDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String registerStartDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String registerEndDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    /** 争议ID */
    @Excel(name = "争议ID")
    private String disputeId;

    /** 争议状态：null-无争议, REQUIRES_RESPONSE-需要响应, ACCEPTED-接受争议, CHALLENGED-已挑战 */
    @Excel(name = "争议状态")
    private String disputeStatus;

    /** 争议类型 */
    @Excel(name = "争议类型")
    private String disputeType;

    /** 争议原因 */
    @Excel(name = "争议原因")
    private String disputeReason;

    /** 争议金额 */
    @Excel(name = "争议金额")
    private BigDecimal disputeAmount;

    /** 争议时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "争议时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date disputeTime;

    /** 争议处理结果 */
    @Excel(name = "争议处理结果")
    private String disputeResult;

    /** 争议处理时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "争议处理时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date disputeProcessTime;

    /**
     * facebook像素ID
     */
    private Long pixelId;

    private Integer pixelStatus;

    /** 是否为邮件营销订单 0-否 1-是 */
    @Excel(name = "是否为邮件营销订单")
    private Integer isEmailMarketingOrder;

    public Date getPayTime() {
        return payTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getAppId() {
        return appId;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getVipId() {
        return vipId;
    }

    public void setVipId(Long vipId) {
        this.vipId = vipId;
    }

    public Long getLinkId() {
        return linkId;
    }

    public void setLinkId(Long linkId) {
        this.linkId = linkId;
    }

    public String getAdid() {
        return adid;
    }

    public void setAdid(String adid) {
        this.adid = adid;
    }

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public String getOrdersn() {
        return ordersn;
    }

    public void setOrdersn(String ordersn) {
        this.ordersn = ordersn;
    }

    public String getMerchantOrderId() {
        return merchantOrderId;
    }

    public void setMerchantOrderId(String merchantOrderId) {
        this.merchantOrderId = merchantOrderId;
    }

    public String getPaymentIntentId() {
        return paymentIntentId;
    }

    public void setPaymentIntentId(String paymentIntentId) {
        this.paymentIntentId = paymentIntentId;
    }

    public String getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getPaymentCurrency() {
        return paymentCurrency;
    }

    public void setPaymentCurrency(String paymentCurrency) {
        this.paymentCurrency = paymentCurrency;
    }

    public BigDecimal getPaymentAmount() {
        return paymentAmount;
    }

    public void setPaymentAmount(BigDecimal paymentAmount) {
        this.paymentAmount = paymentAmount;
    }

    public String getFeeCurrency() {
        return feeCurrency;
    }

    public void setFeeCurrency(String feeCurrency) {
        this.feeCurrency = feeCurrency;
    }

    public BigDecimal getFeeAmount() {
        return feeAmount;
    }

    public void setFeeAmount(BigDecimal feeAmount) {
        this.feeAmount = feeAmount;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getClientSecret() {
        return clientSecret;
    }

    public void setClientSecret(String clientSecret) {
        this.clientSecret = clientSecret;
    }

    public String getRawData() {
        return rawData;
    }

    public void setRawData(String rawData) {
        this.rawData = rawData;
    }

    public Date getLinkTime() {
        return linkTime;
    }

    public void setLinkTime(Date linkTime) {
        this.linkTime = linkTime;
    }

    public String getOther() {
        return other;
    }

    public void setOther(String other) {
        this.other = other;
    }

    public String getPushState() {
        return pushState;
    }

    public void setPushState(String pushState) {
        this.pushState = pushState;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getVipName() {
        return vipName;
    }

    public void setVipName(String vipName) {
        this.vipName = vipName;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getCreatedStartDate() {
        return createdStartDate;
    }

    public void setCreatedStartDate(String createdStartDate) {
        this.createdStartDate = createdStartDate;
    }

    public String getCreatedEndDate() {
        return createdEndDate;
    }

    public void setCreatedEndDate(String createdEndDate) {
        this.createdEndDate = createdEndDate;
    }

    public String getRegisterStartDate() {
        return registerStartDate;
    }

    public void setRegisterStartDate(String registerStartDate) {
        this.registerStartDate = registerStartDate;
    }

    public String getRegisterEndDate() {
        return registerEndDate;
    }

    public void setRegisterEndDate(String registerEndDate) {
        this.registerEndDate = registerEndDate;
    }

    public String getDisputeId() {
        return disputeId;
    }

    public void setDisputeId(String disputeId) {
        this.disputeId = disputeId;
    }

    public String getDisputeStatus() {
        return disputeStatus;
    }

    public void setDisputeStatus(String disputeStatus) {
        this.disputeStatus = disputeStatus;
    }

    public String getDisputeType() {
        return disputeType;
    }

    public void setDisputeType(String disputeType) {
        this.disputeType = disputeType;
    }

    public String getDisputeReason() {
        return disputeReason;
    }

    public void setDisputeReason(String disputeReason) {
        this.disputeReason = disputeReason;
    }

    public BigDecimal getDisputeAmount() {
        return disputeAmount;
    }

    public void setDisputeAmount(BigDecimal disputeAmount) {
        this.disputeAmount = disputeAmount;
    }

    public Date getDisputeTime() {
        return disputeTime;
    }

    public void setDisputeTime(Date disputeTime) {
        this.disputeTime = disputeTime;
    }

    public String getDisputeResult() {
        return disputeResult;
    }

    public void setDisputeResult(String disputeResult) {
        this.disputeResult = disputeResult;
    }

    public Date getDisputeProcessTime() {
        return disputeProcessTime;
    }

    public void setDisputeProcessTime(Date disputeProcessTime) {
        this.disputeProcessTime = disputeProcessTime;
    }

    public Integer getIsEmailMarketingOrder() {
        return isEmailMarketingOrder;
    }

    public void setIsEmailMarketingOrder(Integer isEmailMarketingOrder) {
        this.isEmailMarketingOrder = isEmailMarketingOrder;
    }

    public Long getPixelId() {
        return pixelId;
    }

    public void setPixelId(Long pixelId) {
        this.pixelId = pixelId;
    }
}
