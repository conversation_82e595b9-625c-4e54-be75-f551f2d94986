package com.ruoyi.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 用户管理对象 short_user
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
public class ShortUserDo
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 应用 */
    @Excel(name = "应用")
    private Long appId;

    /** vip */
    @Excel(name = "vip")
    private Long vipId;

    /** 充值模板
 */
    @Excel(name = "充值模板")
    private Long payTemplateId;

    /**  链接id 用户订单实时保存用户进入的链接id
 */
    @Excel(name = " 链接id 用户订单实时保存用户进入的链接id")
    private Long linkidId;

    @Excel(name = "订阅类型")
    private String subscriptionType;

    private String beginTime;

    private String endTime;

    @Excel(name = "vip名称")
    private String vipName;

    @Excel(name = "price")
    private BigDecimal price;

    @TableField(exist = false)
    @JsonIgnore
    private String searchValue;

    /** 创建者 */
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /** 更新者 */
    private String updateBy;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /** 备注 */
    private String remark;

    @Excel(name = "广告名称")
    private String adName;

    @Excel(name = "深链进入名称")
    private String linkname;

    private Integer unsub;

    private String payType;

    private Integer type;

    private Long pixelId;

    private Integer pixelStatus;

    private Boolean payByEmailFlag;

    private Integer validStatus;

    public Integer getValidStatus() {
        return validStatus;
    }

    public void setValidStatus(Integer validStatus) {
        this.validStatus = validStatus;
    }

    public Boolean getPayByEmailFlag() {
        return payByEmailFlag;
    }

    public void setPayByEmailFlag(Boolean payByEmailFlag) {
        this.payByEmailFlag = payByEmailFlag;
    }

    public String getAdName() {
        return adName;
    }

    public void setAdName(String adName) {
        this.adName = adName;
    }

    public Integer getPixelStatus() {
        return pixelStatus;
    }

    public void setPixelStatus(Integer pixelStatus) {
        this.pixelStatus = pixelStatus;
    }

    public Long getPixelId() {
        return pixelId;
    }

    public void setPixelId(Long pixelId) {
        this.pixelId = pixelId;
    }

    /**
     * facebook像素ID
     */
    private String facebookPixelId;

    public String getFacebookPixelId() {
        return facebookPixelId;
    }

    public void setFacebookPixelId(String facebookPixelId) {
        this.facebookPixelId = facebookPixelId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public Integer getUnsub() {
        return unsub;
    }

    public void setUnsub(Integer unsub) {
        this.unsub = unsub;
    }

    public String getLinkname() {
        return linkname;
    }

    public void setLinkname(String linkname) {
        this.linkname = linkname;
    }

    public String getSearchValue() {
        return searchValue;
    }

    public void setSearchValue(String searchValue) {
        this.searchValue = searchValue;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getVipName() {
        return vipName;
    }

    public void setVipName(String vipName) {
        this.vipName = vipName;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getSubscriptionType() {
        return subscriptionType;
    }

    public void setSubscriptionType(String subscriptionType) {
        this.subscriptionType = subscriptionType;
    }

    public void setLinkidId(Long linkidId) {
        this.linkidId = linkidId;
    }

    public int getCountType() {
        return countType;
    }

    public void setCountType(int countType) {
        this.countType = countType;
    }

    /** 用户名 */
    @Excel(name = "用户名")
    private String username;

    /** 密码 */
    @Excel(name = "密码")
    private String password;

    /** 头像
 */
    @Excel(name = "头像")
    private String avatar;

    /** 状态 1为正常 0为禁用
 */
    @Excel(name = "状态 1为正常 0为禁用")
    private String state;

    /** 用户来源:web,android,ios，fb，google，apple等等来源渠道
 */
    @Excel(name = "用户来源:web,android,ios，fb，google，apple等等来源渠道")
    private String source;

    /** 邮箱 */
    @Excel(name = "邮箱")
    private String email;

    /** 手机型号
 */
    @Excel(name = "手机型号")
    private String phoneVersion;

    /** 系统版本
 */
    @Excel(name = "系统版本")
    private String systemVersion;

    @Excel(name = "国家")
    private String country;

    /** 语言 */
    @Excel(name = "语言")
    private String language;

    /** app版本
 */
    @Excel(name = "app版本")
    private String appVersion;

    /** 账号类型 */
    @Excel(name = "账号类型")
    private String accountType;

    /** 唯一标识 */
    @Excel(name = "唯一标识")
    private String uniqueId;

    /** ip */
    @Excel(name = "ip")
    private String ip;

    /** 第三方唯一id
 */
    @Excel(name = "第三方唯一id")
    private String subId;

    /** 用户token
 */
    @Excel(name = "用户token")
    private String token;

    /** 金币数量 */
    @Excel(name = "金币数量")
    private Long coin;

    /** 签到天数 */
    @Excel(name = "签到天数")
    private Long signInDays;

    /** 最后签到时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "最后签到时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date lastSignIn;

    /** 会员过期时间 (因为按照单集购买，所以过期时间一般取订阅用户的结束时间)
 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "会员过期时间 (因为按照单集购买，所以过期时间一般取订阅用户的结束时间)", width = 30, dateFormat = "yyyy-MM-dd")
    private LocalDateTime expireTime;

    /** 其他信息，比如 UA、fbp（Facebook Pixel 生成的 _fbp Cookie 值）
 */
    @Excel(name = "其他信息，比如 UA、fbp", readConverterExp = "F=acebook,P=ixel,生=成的,_=fbp,C=ookie,值=")
    private String other;

    /** 是否为首次付费 */
    @Excel(name = "是否为首次付费")
    private String isFirstPay;

    /** 推送次数 */
    @Excel(name = "推送次数")
    private Long pushNum;

    /** 用户混淆ID
 */
    @Excel(name = "用户混淆ID")
    private String hashId;

    /** 是否订阅 */
    @Excel(name = "是否订阅")
    private String isSubscriber;

    /** 支付信息 */
    @Excel(name = "支付信息")
    private String payInfo;

    /** 链接时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "链接时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date linkTime;

    /** 备注 */
    @Excel(name = "备注")
    private String note;

    /** 续费成功次数 */
    @Excel(name = "续费成功次数")
    private Long renewalOk;

    /** 续费失败次数 */
    @Excel(name = "续费失败次数")
    private Long renewalNo;

    /** 用户类型 */
    @Excel(name = "用户类型")
    private String userType;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    @Excel(name = "应用名称")
    private String appName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date addtime;

    @Excel(name = "查询用户或名称")
    private String userIdOrName;


    public String getUserIdOrName() {
        return userIdOrName;
    }

    public void setUserIdOrName(String userIdOrName) {
        this.userIdOrName = userIdOrName;
    }

    private int countType;


    public Date getAddtime() {
        return addtime;
    }

    public void setAddtime(Date addtime) {
        this.addtime = addtime;
    }


    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getAppName() {
        return appName;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }

    public void setAppId(Long appId)
    {
        this.appId = appId;
    }

    public Long getAppId()
    {
        return appId;
    }

    public void setVipId(Long vipId)
    {
        this.vipId = vipId;
    }

    public Long getVipId()
    {
        return vipId;
    }

    public void setPayTemplateId(Long payTemplateId)
    {
        this.payTemplateId = payTemplateId;
    }

    public Long getPayTemplateId()
    {
        return payTemplateId;
    }

    public void setLinkId(Long linkId)
    {
        this.linkidId = linkId;
    }

    public Long getLinkidId()
    {
        return linkidId;
    }

    public void setUsername(String username)
    {
        this.username = username;
    }

    public String getUsername()
    {
        return username;
    }

    public void setPassword(String password)
    {
        this.password = password;
    }

    public String getPassword()
    {
        return password;
    }

    public void setAvatar(String avatar)
    {
        this.avatar = avatar;
    }

    public String getAvatar()
    {
        return avatar;
    }

    public void setState(String state)
    {
        this.state = state;
    }

    public String getState()
    {
        return state;
    }

    public void setSource(String source)
    {
        this.source = source;
    }

    public String getSource()
    {
        return source;
    }

    public void setEmail(String email)
    {
        this.email = email;
    }

    public String getEmail()
    {
        return email;
    }

    public void setPhoneVersion(String phoneVersion)
    {
        this.phoneVersion = phoneVersion;
    }

    public String getPhoneVersion()
    {
        return phoneVersion;
    }

    public void setSystemVersion(String systemVersion)
    {
        this.systemVersion = systemVersion;
    }

    public String getSystemVersion()
    {
        return systemVersion;
    }

    public void setLanguage(String language)
    {
        this.language = language;
    }

    public String getLanguage()
    {
        return language;
    }

    public void setAppVersion(String appVersion)
    {
        this.appVersion = appVersion;
    }

    public String getAppVersion()
    {
        return appVersion;
    }

    public void setAccountType(String accountType)
    {
        this.accountType = accountType;
    }

    public String getAccountType()
    {
        return accountType;
    }

    public void setUniqueId(String uniqueId)
    {
        this.uniqueId = uniqueId;
    }

    public String getUniqueId()
    {
        return uniqueId;
    }

    public void setIp(String ip)
    {
        this.ip = ip;
    }

    public String getIp()
    {
        return ip;
    }

    public void setSubId(String subId)
    {
        this.subId = subId;
    }

    public String getSubId()
    {
        return subId;
    }

    public void setToken(String token)
    {
        this.token = token;
    }

    public String getToken()
    {
        return token;
    }

    public void setCoin(Long coin)
    {
        this.coin = coin;
    }

    public Long getCoin()
    {
        return coin;
    }

    public void setSignInDays(Long signInDays)
    {
        this.signInDays = signInDays;
    }

    public Long getSignInDays()
    {
        return signInDays;
    }

    public void setLastSignIn(Date lastSignIn)
    {
        this.lastSignIn = lastSignIn;
    }

    public Date getLastSignIn()
    {
        return lastSignIn;
    }

    public void setExpireTime(LocalDateTime expireTime)
    {
        this.expireTime = expireTime;
    }

    public LocalDateTime getExpireTime()
    {
        return expireTime;
    }

    public void setOther(String other)
    {
        this.other = other;
    }

    public String getOther()
    {
        return other;
    }

    public void setIsFirstPay(String isFirstPay)
    {
        this.isFirstPay = isFirstPay;
    }

    public String getIsFirstPay()
    {
        return isFirstPay;
    }

    public void setPushNum(Long pushNum)
    {
        this.pushNum = pushNum;
    }

    public Long getPushNum()
    {
        return pushNum;
    }

    public void setHashId(String hashId)
    {
        this.hashId = hashId;
    }

    public String getHashId()
    {
        return hashId;
    }

    public void setIsSubscriber(String isSubscriber)
    {
        this.isSubscriber = isSubscriber;
    }

    public String getIsSubscriber()
    {
        return isSubscriber;
    }

    public void setPayInfo(String payInfo)
    {
        this.payInfo = payInfo;
    }

    public String getPayInfo()
    {
        return payInfo;
    }

    public void setLinkTime(Date linkTime)
    {
        this.linkTime = linkTime;
    }

    public Date getLinkTime()
    {
        return linkTime;
    }

    public void setNote(String note)
    {
        this.note = note;
    }

    public String getNote()
    {
        return note;
    }

    public void setRenewalOk(Long renewalOk)
    {
        this.renewalOk = renewalOk;
    }

    public Long getRenewalOk()
    {
        return renewalOk;
    }

    public void setRenewalNo(Long renewalNo)
    {
        this.renewalNo = renewalNo;
    }

    public Long getRenewalNo()
    {
        return renewalNo;
    }

    public void setUserType(String userType)
    {
        this.userType = userType;
    }

    public String getUserType()
    {
        return userType;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("appId", getAppId())
            .append("vipId", getVipId())
            .append("payTemplateId", getPayTemplateId())
            .append("linkidId", getLinkidId())
            .append("username", getUsername())
            .append("password", getPassword())
            .append("avatar", getAvatar())
            .append("state", getState())
            .append("source", getSource())
            .append("email", getEmail())
            .append("phoneVersion", getPhoneVersion())
            .append("systemVersion", getSystemVersion())
            .append("language", getLanguage())
            .append("appVersion", getAppVersion())
            .append("accountType", getAccountType())
            .append("uniqueId", getUniqueId())
            .append("ip", getIp())
            .append("subId", getSubId())
            .append("token", getToken())
            .append("coin", getCoin())
            .append("signInDays", getSignInDays())
            .append("lastSignIn", getLastSignIn())
            .append("expireTime", getExpireTime())
            .append("other", getOther())
            .append("isFirstPay", getIsFirstPay())
            .append("pushNum", getPushNum())
            .append("hashId", getHashId())
            .append("isSubscriber", getIsSubscriber())
            .append("payInfo", getPayInfo())
            .append("linkTime", getLinkTime())
            .append("note", getNote())
            .append("renewalOk", getRenewalOk())
            .append("renewalNo", getRenewalNo())
            .append("userType", getUserType())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
