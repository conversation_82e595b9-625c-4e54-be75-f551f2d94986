package com.ruoyi.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 邮箱数据统计
 * @TableName short_email_data
 */
@TableName(value ="short_email_data")
@Data
public class ShortEmailData {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 亚马逊发送的次数
     */
    private Integer awsSendNum;

    /**
     * brevo发送次数
     */
    private Integer brevoSendNum;

    /**
     * 支付成功数量
     */
    private Integer payNum;

    /**
     * 当天支付成功数
     */
    private Integer dayPayNum;

    /**
     * 
     */
    private Integer dayAwsSendNum;

    /**
     * 
     */
    private Integer dayBrevoSendNum;

    /**
     * 邮件触达用户
     */
    private Integer sendUser;

    /**
     * 当天触达
     */
    private Integer daySendUser;

    /**
     * 当天aws进入页面
     */
    private Integer dayAwsEnterNum;

    /**
     * aws进入页面
     */
    private Integer awsEnterNum;

    /**
     * 当天brveo 进入页面
     */
    private Integer dayBrevoEnterNum;

    /**
     * brveo进入页面次数
     */
    private Integer brveoEnterNum;

    /**
     * 时间
     */
    private Date dayNode;

    /**
     * 当天用户进入次数
     */
    private Integer dayUserEnter;

    /**
     * 用户进入次数
     */
    private Integer userEnter;

    /**
     * 
     */
    private Date createTime;

    /**
     * 
     */
    private Date updateTime;
}