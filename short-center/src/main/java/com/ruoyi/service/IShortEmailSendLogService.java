package com.ruoyi.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.domain.ShortEmailSendLog;
import com.ruoyi.dto.ShortEmailSendLogDTO;
import com.ruoyi.vo.ShortEmailSendLogVO;

import java.util.List;

/**
 * 邮件发送记录Service接口
 *
 * <AUTHOR>
 * @date 2025-05-30
 */
public interface IShortEmailSendLogService
{
    /**
     * 查询邮件发送记录
     *
     * @param id 邮件发送记录主键
     * @return 邮件发送记录
     */
    public ShortEmailSendLog selectShortEmailSendLogById(Long id);

    /**
     * 查询邮件发送记录列表
     *
     * @param shortEmailSendLog 邮件发送记录
     * @return 邮件发送记录集合
     */
    public List<ShortEmailSendLog> selectShortEmailSendLogList(ShortEmailSendLog shortEmailSendLog);

    /**
     * 新增邮件发送记录
     *
     * @param shortEmailSendLog 邮件发送记录
     * @return 结果
     */
    public int insertShortEmailSendLog(ShortEmailSendLog shortEmailSendLog);

    /**
     * 修改邮件发送记录
     *
     * @param shortEmailSendLog 邮件发送记录
     * @return 结果
     */
    public int updateShortEmailSendLog(ShortEmailSendLog shortEmailSendLog);

    /**
     * 批量删除邮件发送记录
     *
     * @param ids 需要删除的邮件发送记录主键集合
     * @return 结果
     */
    public int deleteShortEmailSendLogByIds(Long[] ids);

    /**
     * 删除邮件发送记录信息
     *
     * @param id 邮件发送记录主键
     * @return 结果
     */
    public int deleteShortEmailSendLogById(Long id);

    List<ShortEmailSendLog> getListByEndTime();

    R<Page<ShortEmailSendLogVO>> emailList(ShortEmailSendLogDTO sendLogDTO);
}
