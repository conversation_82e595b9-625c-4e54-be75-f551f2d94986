package com.ruoyi.service;

import java.util.List;
import com.ruoyi.domain.ShortApp;

/**
 * App管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
public interface IShortAppService 
{
    /**
     * 查询App管理
     * 
     * @param id App管理主键
     * @return App管理
     */
    public ShortApp selectShortAppById(Long id);

    /**
     * 查询App管理列表
     * 
     * @param shortApp App管理
     * @return App管理集合
     */
    public List<ShortApp> selectShortAppList(ShortApp shortApp);

    /**
     * 新增App管理
     * 
     * @param shortApp App管理
     * @return 结果
     */
    public int insertShortApp(ShortApp shortApp);

    /**
     * 修改App管理
     * 
     * @param shortApp App管理
     * @return 结果
     */
    public int updateShortApp(ShortApp shortApp);

    /**
     * 批量删除App管理
     * 
     * @param ids 需要删除的App管理主键集合
     * @return 结果
     */
    public int deleteShortAppByIds(Long[] ids);

    /**
     * 删除App管理信息
     * 
     * @param id App管理主键
     * @return 结果
     */
    public int deleteShortAppById(Long id);
}
