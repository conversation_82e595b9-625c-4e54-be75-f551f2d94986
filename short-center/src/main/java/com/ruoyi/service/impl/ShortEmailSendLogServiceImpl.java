package com.ruoyi.service.impl;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.domain.ShortEmailSendLog;
import com.ruoyi.dto.ShortEmailSendLogDTO;
import com.ruoyi.mapper.ShortEmailSendLogMapper;
import com.ruoyi.service.IShortEmailSendLogService;
import com.ruoyi.service.IShortIntermedStatusService;
import com.ruoyi.vo.ShortEmailSendLogVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 邮件发送记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-30
 */
@Slf4j
@Service
public class ShortEmailSendLogServiceImpl implements IShortEmailSendLogService
{
    @Autowired
    private ShortEmailSendLogMapper shortEmailSendLogMapper;

    @Autowired
    private IShortIntermedStatusService shortIntermedStatusService;

    /**
     * 查询邮件发送记录
     *
     * @param id 邮件发送记录主键
     * @return 邮件发送记录
     */
    @Override
    public ShortEmailSendLog selectShortEmailSendLogById(Long id)
    {
        return shortEmailSendLogMapper.selectShortEmailSendLogById(id);
    }

    /**
     * 查询邮件发送记录列表
     *
     * @param shortEmailSendLog 邮件发送记录
     * @return 邮件发送记录
     */
    @Override
    public List<ShortEmailSendLog> selectShortEmailSendLogList(ShortEmailSendLog shortEmailSendLog)
    {
        return shortEmailSendLogMapper.selectShortEmailSendLogList(shortEmailSendLog);
    }

    /**
     * 新增邮件发送记录
     *
     * @param shortEmailSendLog 邮件发送记录
     * @return 结果
     */
    @Override
    public int insertShortEmailSendLog(ShortEmailSendLog shortEmailSendLog)
    {
        shortEmailSendLog.setCreateTime(DateUtils.getNowDate());
        return shortEmailSendLogMapper.insertShortEmailSendLog(shortEmailSendLog);
    }

    /**
     * 修改邮件发送记录
     *
     * @param shortEmailSendLog 邮件发送记录
     * @return 结果
     */
    @Override
    public int updateShortEmailSendLog(ShortEmailSendLog shortEmailSendLog)
    {
        shortEmailSendLog.setUpdateTime(DateUtils.getNowDate());
        return shortEmailSendLogMapper.updateShortEmailSendLog(shortEmailSendLog);
    }

    /**
     * 批量删除邮件发送记录
     *
     * @param ids 需要删除的邮件发送记录主键
     * @return 结果
     */
    @Override
    public int deleteShortEmailSendLogByIds(Long[] ids)
    {
        return shortEmailSendLogMapper.deleteShortEmailSendLogByIds(ids);
    }

    /**
     * 删除邮件发送记录信息
     *
     * @param id 邮件发送记录主键
     * @return 结果
     */
    @Override
    public int deleteShortEmailSendLogById(Long id)
    {
        return shortEmailSendLogMapper.deleteShortEmailSendLogById(id);
    }

    @Override
    public List<ShortEmailSendLog> getListByEndTime() {
        return shortEmailSendLogMapper.getListByEndTime();
    }

    @Override
    public R<Page<ShortEmailSendLogVO>> emailList(ShortEmailSendLogDTO sendLogDTO) {
        Page<ShortEmailSendLogVO> page = new Page<>(sendLogDTO.getPageNum(), sendLogDTO.getPageSize());
        Page<ShortEmailSendLogVO> voListPage =  shortEmailSendLogMapper.emailList(page,sendLogDTO);

        return R.ok(voListPage);
    }
}
