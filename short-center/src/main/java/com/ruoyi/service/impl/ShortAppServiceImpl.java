package com.ruoyi.service.impl;

import java.util.List;

import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.mapper.ShortAppMapper;
import com.ruoyi.domain.ShortApp;
import com.ruoyi.service.IShortAppService;

/**
 * App管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
@Service
public class ShortAppServiceImpl implements IShortAppService 
{
    @Autowired
    private ShortAppMapper shortAppMapper;

    /**
     * 查询App管理
     * 
     * @param id App管理主键
     * @return App管理
     */
    @Override
    public ShortApp selectShortAppById(Long id)
    {

        return shortAppMapper.selectShortAppById(id);
    }

    /**
     * 查询App管理列表
     * 
     * @param shortApp App管理
     * @return App管理
     */
    @Override
    public List<ShortApp> selectShortAppList(ShortApp shortApp)
    {
        return shortAppMapper.selectShortAppList(shortApp);
    }

    /**
     * 新增App管理
     * 
     * @param shortApp App管理
     * @return 结果
     */
    @Override
    public int insertShortApp(ShortApp shortApp)
    {
        shortApp.setType(null!=shortApp.getType()?shortApp.getType():"0");
        shortApp.setCreateTime(DateUtils.getNowDate());
        shortApp.setUpdateTime(DateUtils.getNowDate());
        return shortAppMapper.insertShortApp(shortApp);
    }

    /**
     * 修改App管理
     * 
     * @param shortApp App管理
     * @return 结果
     */
    @Override
    public int updateShortApp(ShortApp shortApp)
    {
        shortApp.setUpdateTime(DateUtils.getNowDate());
        return shortAppMapper.updateShortApp(shortApp);
    }

    /**
     * 批量删除App管理
     * 
     * @param ids 需要删除的App管理主键
     * @return 结果
     */
    @Override
    public int deleteShortAppByIds(Long[] ids)
    {
        return shortAppMapper.deleteShortAppByIds(ids);
    }

    /**
     * 删除App管理信息
     * 
     * @param id App管理主键
     * @return 结果
     */
    @Override
    public int deleteShortAppById(Long id)
    {
        return shortAppMapper.deleteShortAppById(id);
    }
}
