package com.ruoyi.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.KeyPair;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.domain.*;
import com.ruoyi.dto.ShortOrderDTO;
import com.ruoyi.mapper.*;
import com.ruoyi.service.IShortCountLogService;
import com.ruoyi.service.IShortIntermedStatusService;
import com.ruoyi.service.IShortOrderService;
import com.ruoyi.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLDecoder;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 订单表Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
@Slf4j
@Service
public class ShortOrderServiceImpl extends ServiceImpl<ShortOrderMapper, ShortOrder> implements IShortOrderService {
    @Autowired
    private ShortOrderMapper shortOrderMapper;

    @Autowired
    private ShortUserMapper shortUserMapper;

    @Autowired
    private ShortAppMapper shortAppMapper;

    @Autowired
    private ShortVipMapper shortVipMapper;

    @Autowired
    private ShortMovieMapper shortMovieMapper;

    @Autowired
    private IShortIntermedStatusService shortIntermedStatusService;

    @Autowired
    private IShortCountLogService shortCountLogService;


    /**
     * 查询订单表
     *
     * @param id 订单表主键
     * @return 订单表
     */
    @Override
    public ShortOrder selectShortOrderById(Long id) {
        ShortOrder s = shortOrderMapper.selectShortOrderById(id);
        ShortApp app = shortAppMapper.selectShortAppById(s.getAppId());
        if (null != app)
            s.setAppName(app.getName());
        ShortUser user = shortUserMapper.selectShortUserById(s.getUserId());
        if (null != user)
            s.setUserName(user.getUsername());

        ShortVip shortVip = shortVipMapper.selectShortVipById(s.getVipId());
        if (null != shortVip)
            s.setVipName(shortVip.getName());
        return s;
    }

    /**
     * 查询订单表列表
     *
     * @param shortOrder 订单表
     * @return 订单表
     */
    @Override
    public List<ShortOrder> selectShortOrderList(ShortOrder shortOrder) {
        List<ShortOrder> list = shortOrderMapper.selectShortOrderList(shortOrder);
        for (ShortOrder s : list) {
            ShortApp app = shortAppMapper.selectShortAppById(s.getAppId());
            if (null != app)
                s.setAppName(app.getName());
            ShortUser user = shortUserMapper.selectShortUserById(s.getUserId());
            if (null != user)
                s.setUserName(user.getUsername());

            ShortVip shortVip = shortVipMapper.selectShortVipById(s.getVipId());
            if (null != shortVip)
                s.setVipName(shortVip.getName());
        }
        return list;
    }

    /**
     * 新增订单表
     *
     * @param shortOrder 订单表
     * @return 结果
     */
    @Override
    public int insertShortOrder(ShortOrder shortOrder) {
        shortOrder.setCreateTime(DateUtils.getNowDate());
        shortOrder.setPixelStatus(null != shortOrder.getPixelStatus() ? shortOrder.getPixelStatus(): 0);
        return shortOrderMapper.insertShortOrder(shortOrder);
    }

    /**
     * 修改订单表
     *
     * @param shortOrder 订单表
     * @return 结果
     */
    @Override
    public int updateShortOrder(ShortOrder shortOrder) {
        shortOrder.setUpdateTime(DateUtils.getNowDate());
        return shortOrderMapper.updateShortOrder(shortOrder);
    }

    /**
     * 批量删除订单表
     *
     * @param ids 需要删除的订单表主键
     * @return 结果
     */
    @Override
    public int deleteShortOrderByIds(Long[] ids) {
        return shortOrderMapper.deleteShortOrderByIds(ids);
    }

    /**
     * 删除订单表信息
     *
     * @param id 订单表主键
     * @return 结果
     */
    @Override
    public int deleteShortOrderById(Long id) {
        return shortOrderMapper.deleteShortOrderById(id);
    }

    @Override
    public ShortOrderOverViewVO orderOverView() {
        ShortOrderOverViewVO orderOverView = shortOrderMapper.orderOverView();
        if (ObjectUtil.isNotNull(orderOverView)) {
            orderOverView.setPayTopCountry(shortOrderMapper.payTopCountry());
        }
        return orderOverView;
    }

    @Override
    public ShortOrderStatisticVO orderStatisticView(ShortOrderDTO orderDTO) {
        if (null != orderDTO.getRegisterStartDate() && null != orderDTO.getRegisterEndDate()) {
            orderDTO.setRegisterStartDate(orderDTO.getRegisterStartDate() + " 00:00:00");
            orderDTO.setRegisterEndDate(orderDTO.getRegisterEndDate() + " 23:59:59");
        }
        if (null != orderDTO.getPaymentStartDate() && null != orderDTO.getPaymentEndDate()) {
            orderDTO.setPaymentStartDate(orderDTO.getPaymentStartDate() + " 00:00:00");
            orderDTO.setPaymentEndDate(orderDTO.getPaymentEndDate() + " 23:59:59");
        }
        return shortOrderMapper.orderStatisticView(orderDTO);
    }

    @Override
    public List<ShortOrderVO> orderPageList(ShortOrderDTO orderDTO) {
        if (null != orderDTO.getRegisterStartDate() && null != orderDTO.getRegisterEndDate()) {
            orderDTO.setRegisterStartDate(orderDTO.getRegisterStartDate() + " 00:00:00");
            orderDTO.setRegisterEndDate(orderDTO.getRegisterEndDate() + " 23:59:59");
        }
        if (null != orderDTO.getPaymentStartDate() && null != orderDTO.getPaymentEndDate()) {
            orderDTO.setPaymentStartDate(orderDTO.getPaymentStartDate() + " 00:00:00");
            orderDTO.setPaymentEndDate(orderDTO.getPaymentEndDate() + " 23:59:59");
        }
        //针对异步调用查询订单不会携带token 从token获取userId 会报错 从其他地方传入进行查询
        Long loggedInUserId = null == orderDTO.getExportUserId() ? SecurityUtils.getUserId() : orderDTO.getExportUserId() ;
        Long filterUserId = (loggedInUserId != null && loggedInUserId == 1) ? null : loggedInUserId;

        List<ShortOrderVO> shortOrderVOS = shortOrderMapper.orderPageList(orderDTO, filterUserId);
        if (CollectionUtil.isNotEmpty(shortOrderVOS)) {
            //查询剧名
            List<Long> movieIdCollect = shortOrderVOS.stream().map(ShortOrderVO::getMovieId).distinct().collect(Collectors.toList());
            List<ShortMovie> shortMovies = shortMovieMapper.selectShortMovieByIds(movieIdCollect);
            Map<Long, ShortMovie> movieMap = new HashMap<>();
            if(CollectionUtil.isNotEmpty(shortMovies)){
                movieMap = shortMovies.stream().collect(Collectors.toMap(ShortMovie::getId, Function.identity(), (v1, v2) -> v1));
            }
            for (ShortOrderVO shortOrderVO : shortOrderVOS) {
                String other = shortOrderVO.getOther();
                if (StringUtils.isNotEmpty(other)) {
                    JSONObject jsonObject = JSONUtil.parseObj(other);
                    try {
                        if (jsonObject.containsKey("adName") && StringUtils.isNotEmpty(jsonObject.getStr("adName"))) {
                            //url编码转为中文
                            shortOrderVO.setAdName(URLDecoder.decode(jsonObject.getStr("adName"), "UTF-8"));
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                //设置剧名
                if(movieMap.containsKey(shortOrderVO.getMovieId())){
                    shortOrderVO.setMovieName(movieMap.get(shortOrderVO.getMovieId()).getName());
                }
                //设置支付类型 订阅 订阅续费 拼接天数
                if("订阅".equals(shortOrderVO.getPayType()) || "订阅续费".equals(shortOrderVO.getPayType())){
                    shortOrderVO.setPayTypeStr(shortOrderVO.getSubscriptionType() + "日" + shortOrderVO.getPayType());
                }else {
                    shortOrderVO.setPayTypeStr(shortOrderVO.getPayType());
                }
            }
        }
        return shortOrderVOS;
    }

    /**
     * 查询最新的未支付订单
     */
    @Override
    public ShortOrder findLatestUnpaidOrder(Long userId, Long appId, Long vipId) {
        // 创建查询条件
        ShortOrder queryOrder = new ShortOrder();
        queryOrder.setUserId(userId);
        queryOrder.setAppId(appId);
        queryOrder.setVipId(vipId);

        // 实际应用中需要根据订单状态进行筛选
        // 这里假设状态为"REQUIRES_PAYMENT_METHOD"的为未支付订单
        queryOrder.setStatus("REQUIRES_PAYMENT_METHOD");

        // 按创建时间降序排列，获取最新的一条记录
        List<ShortOrder> orderList = selectShortOrderList(queryOrder);

        if (orderList != null && !orderList.isEmpty()) {
            return orderList.get(0);
        }

        return null;
    }

    @Override
    public List<ShortOrder> selectByPaymentIntentId(String pid) {
        return shortOrderMapper.selectByPaymentIntentId(pid);
    }

    @Override
    public ShortOrder findFirstByUserAndPay_typeAndStatusOrderByUpdatedAtDesc(Long userId, String payType, String status) {
        return shortOrderMapper.findFirstByUserAndPay_typeAndStatusOrderByUpdatedAtDesc(userId, payType, status);
    }

    @Override
    public ShortOrderDashboardVO dashboardStatistics(ShortOrderDTO orderDTO) {
        Long loggedInUserId = SecurityUtils.getUserId();
        Long filterUserId = (loggedInUserId != null && loggedInUserId == 1) ? null : loggedInUserId;

        formatDateParams(orderDTO);

        ShortOrderDashboardVO result = shortOrderMapper.dashboardStatistics(orderDTO, filterUserId);

        if (result == null) {
            return new ShortOrderDashboardVO();
        }

        Long lastPeriodSubscribeUserCount = shortOrderMapper.getLastPeriodSubscribeUserCount(orderDTO, filterUserId);
        result.setLastPeriodSubscribeUserCount(lastPeriodSubscribeUserCount);

        // 计算订阅留存率：当期续订用户数/上期订阅用户数×100%
        if (lastPeriodSubscribeUserCount != null && lastPeriodSubscribeUserCount > 0 && result.getTotalRenewalUserCount() != null) {
            BigDecimal subscribeRetentionRate = new BigDecimal(result.getTotalRenewalUserCount())
                    .divide(new BigDecimal(lastPeriodSubscribeUserCount), 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal(100));
            result.setSubscribeRetentionRate(subscribeRetentionRate);
        } else {
            result.setSubscribeRetentionRate(BigDecimal.ZERO);
        }


        result.setPayRateTopCountries(shortOrderMapper.payRateTopCountries(orderDTO, filterUserId));
        result.setAvgPayTopCountries(shortOrderMapper.avgPayTopCountries(orderDTO, filterUserId));
        result.setSubscribeRatioTopCountries(shortOrderMapper.subscribeRatioTopCountries(orderDTO, filterUserId));
        result.setRetentionRateTopCountries(shortOrderMapper.retentionRateTopCountries(orderDTO, filterUserId));
        result.setUserRateTopCountries(shortOrderMapper.userRateTopCountries(orderDTO, filterUserId));
        return result;
    }

    @Override
    public ShortOrderDailyVO dailyStatistics(ShortOrderDTO orderDTO) {
        Long loggedInUserId = SecurityUtils.getUserId();
        Long filterUserId = (loggedInUserId != null && loggedInUserId == 1) ? null : loggedInUserId;

        formatDateParams(orderDTO);

        ShortOrderDailyVO result= new ShortOrderDailyVO();
        if(orderDTO.getCountType()>0){
            result = shortOrderMapper.dailyStatistics(orderDTO, filterUserId);
        }else{
            result = shortOrderMapper.dailyStatisticsReal(orderDTO, filterUserId);
        }

        if (StringUtils.isEmpty(orderDTO.getPaymentStartDate()) && StringUtils.isEmpty(orderDTO.getRegisterStartDate())) {
            log.info("无起始时间，默认当日之前的续订金额");
            Date date = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String formattedDate = sdf.format(date);
            BigDecimal oldUserRenewAmount = shortOrderMapper.getOldUserRenewAmount(orderDTO, filterUserId, formattedDate);
            result.setOldUserRenewAmount(oldUserRenewAmount);
        }
        if (StringUtils.isNotEmpty(orderDTO.getPaymentStartDate()) || StringUtils.isNotEmpty(orderDTO.getRegisterStartDate())) {
            log.info("有起始时间，获取起始时间之前的续订金额");
            BigDecimal oldUserRenewAmount = shortOrderMapper.getOldUserRenewAmountByStartDate(orderDTO, filterUserId);
            result.setOldUserRenewAmount(oldUserRenewAmount);
        }
        log.info("获取深链用户数据");
        ShortUserForLinkVO vo = shortUserMapper.getSystemAddUserCount(orderDTO);
        result.setShortUserForLinkVO(vo);
        log.info("重新计算支付率、ios支付率、安卓支付率");

        result.setPayRate(BigDecimalUtil(BigDecimal.valueOf(result.getPayUserCount()), BigDecimal.valueOf(vo.getLinkUser())));
        result.setIosPayRate(BigDecimalUtil(BigDecimal.valueOf(result.getIosPayUserCount()), BigDecimal.valueOf(vo.getIosUser())));
        result.setAndroidPayRate(BigDecimalUtil(BigDecimal.valueOf(result.getAndroidPayUserCount()), BigDecimal.valueOf(vo.getAndroidUser())));
        return result != null ? result : new ShortOrderDailyVO();
    }

    private static BigDecimal BigDecimalUtil(BigDecimal b1, BigDecimal b2) {
        if (Objects.isNull(b1) || b2.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        return b1.divide(b2, 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
    }

    /**
     * 格式化日期参数
     *
     * @param orderDTO DTO对象
     */
    private void formatDateParams(ShortOrderDTO orderDTO) {
        if (orderDTO.getRegisterStartDate() != null && orderDTO.getRegisterEndDate() != null) {
            orderDTO.setRegisterStartDate(orderDTO.getRegisterStartDate() + " 00:00:00");
            orderDTO.setRegisterEndDate(orderDTO.getRegisterEndDate() + " 23:59:59");
        }

        if (orderDTO.getPaymentStartDate() != null && orderDTO.getPaymentEndDate() != null) {
            orderDTO.setPaymentStartDate(orderDTO.getPaymentStartDate() + " 00:00:00");
            orderDTO.setPaymentEndDate(orderDTO.getPaymentEndDate() + " 23:59:59");
        }
    }

    @Override
    public Integer countSuccessfulOrdersByPixelIdBeforeTime(Long userId, Long pixelId, Date orderTime, Long currentOrderId) {
        return shortOrderMapper.countSuccessfulOrdersByPixelIdBeforeTime(userId, pixelId, orderTime, currentOrderId);
    }

    @Override
    public AjaxResult countLog(String publicKey) {
        int count = shortIntermedStatusService.countByName("count_log");
        if(count>0)
            return AjaxResult.success("自动统计首页数据，请勿重复操作");

        ShortIntermedStatus shortIntermedStatus = new ShortIntermedStatus();
        shortIntermedStatus.setName("count_log");
        shortIntermedStatusService.insertShortIntermedStatus(shortIntermedStatus);


        try {
            String privateKey = "-----BEGIN OPENSSH PRIVATE KEY-----\n" +
                    "b3BlbnNzaC1rZXktdjEAAAAABG5vbmUAAAAEbm9uZQAAAAAAAAABAAAAMwAAAAtzc2gtZW\n" +
                    "QyNTUxOQAAACDdx+eInOus2G8pGSS+paM8QV5SkQdAXGh/e0Z7pRaDRwAAAJiIp66uiKeu\n" +
                    "rgAAAAtzc2gtZWQyNTUxOQAAACDdx+eInOus2G8pGSS+paM8QV5SkQdAXGh/e0Z7pRaDRw\n" +
                    "AAAEDJW6rFB140gGyRJZM+mqoIFj8HAGrGpnuWj+sCJa/06N3H54ic66zYbykZJL6lozxB\n" +
                    "XlKRB0BcaH97RnulFoNHAAAAE2Zlbmd4aW41MjIwQDE2My5jb20BAg==\n" +
                    "-----END OPENSSH PRIVATE KEY-----";

            // 2. 加载密钥对
            KeyPair keyPair = KeyPair.load(new JSch(), privateKey.getBytes(), null);
            byte[] derivedPublicKeyBlob = keyPair.getPublicKeyBlob();

            // 3. 解析用户公钥（按空格分割并解码Base64）
            String[] publicKeyParts = publicKey.split(" ");
            if (publicKeyParts.length < 2) {
                throw new IllegalArgumentException("Invalid public key format");
            }
            byte[] userPublicKeyBlob = Base64.getDecoder().decode(publicKeyParts[1]);

            // 4. 安全比对（避免时序攻击）
            boolean isMatch = MessageDigest.isEqual(derivedPublicKeyBlob, userPublicKeyBlob);
            if(isMatch){
                log.info("验证密钥正确------定时任务");

                List<Long> userIds = shortIntermedStatusService.getSysUserId();
                for (Long userId : userIds) {
//                    Long loggedInUserId = userId;
//                    Long filterUserId = (userId != null && userId == 1) ? null : userId;
                    ShortOrderDTO orderDTO = new ShortOrderDTO();
                    formatDateParams(orderDTO);
                    ShortOrderDashboardVO result = shortOrderMapper.dashboardStatistics(orderDTO, userId);
                    if (null != result) {
                        ShortCountLog shortCountLog = new ShortCountLog();
                        shortCountLog.setType(1l);
                        shortCountLog.setUserId(userId);
                        shortCountLog.setTotalUserCount(result.getTotalUserCount());
                        shortCountLog.setTotalPayUserCount(result.getTotalPayUserCount());
                        shortCountLog.setTotalIosPayUserCount(result.getTotalIosPayUserCount());
                        shortCountLog.setTotalAndroidPayUserCount(result.getTotalAndroidPayUserCount());
                        shortCountLog.setTotalSubscribeUserCount(result.getTotalSubscribeUserCount());
                        shortCountLog.setTotalRenewalUserCount(result.getTotalRenewalUserCount());
                        shortCountLog.setTotalPayAmount(result.getTotalPayAmount());
                        shortCountLog.setTotalIosPayAmount(result.getTotalIosPayAmount());
                        shortCountLog.setTotalAndroidPayAmount(result.getTotalAndroidPayAmount());
                        shortCountLog.setTotalSubscribeAmount(result.getTotalSubscribeAmount());
                        shortCountLog.setTotalIosSubscribeAmount(result.getTotalIosSubscribeAmount());
                        shortCountLog.setTotalAndroidSubscribeAmount(result.getTotalAndroidSubscribeAmount());
                        shortCountLog.setTotalRenewalAmount(result.getTotalRenewalAmount());
                        shortCountLog.setTotalRechargeAmount(result.getTotalRechargeAmount());
                        shortCountLog.setTotalIosRechargeAmount(result.getTotalIosRechargeAmount());
                        shortCountLog.setTotalAndroidRechargeAmount(result.getTotalAndroidRechargeAmount());
                        shortCountLog.setTotalPayRate(result.getTotalPayRate());
                        shortCountLog.setTotalIosPayRate(result.getTotalIosPayRate());
                        shortCountLog.setTotalAndroidPayRate(result.getTotalAndroidPayRate());
                        shortCountLog.setTotalSubscribeRatio(result.getTotalSubscribeRatio());
                        shortCountLog.setTotalIosSubscribeRatio(result.getTotalIosSubscribeRatio());
                        shortCountLog.setTotalAndroidSubscribeRatio(result.getTotalAndroidSubscribeRatio());
                        shortCountLog.setAvgPayAmount(result.getAvgPayAmount());
                        shortCountLog.setAvgSubscribeAmount(result.getAvgSubscribeAmount());
                        shortCountLog.setAvgRenewalAmount(result.getAvgRenewalAmount());
                        shortCountLog.setAvgIosPayAmount(result.getAvgIosPayAmount());
                        shortCountLog.setAvgAndroidPayAmount(result.getAvgAndroidPayAmount());

                        Long lastPeriodSubscribeUserCount = shortOrderMapper.getLastPeriodSubscribeUserCount(orderDTO, userId);
                        shortCountLog.setLastPeriodSubscribeUserCount(lastPeriodSubscribeUserCount);

                        // 计算订阅留存率：当期续订用户数/上期订阅用户数×100%
                        if (lastPeriodSubscribeUserCount != null && lastPeriodSubscribeUserCount > 0 && result.getTotalRenewalUserCount() != null) {
                            BigDecimal subscribeRetentionRate = new BigDecimal(result.getTotalRenewalUserCount())
                                    .divide(new BigDecimal(lastPeriodSubscribeUserCount), 4, RoundingMode.HALF_UP)
                                    .multiply(new BigDecimal(100));
                            shortCountLog.setSubscribeRetentionRate(subscribeRetentionRate);
                        } else {
                            shortCountLog.setSubscribeRetentionRate(BigDecimal.ZERO);
                        }
                        shortCountLogService.insertShortCountLog(shortCountLog);


                        List<CountryStatisticVO> countryStatisticVOList = shortOrderMapper.payRateTopCountries(orderDTO, userId);
                        for (CountryStatisticVO countryStatisticVO : countryStatisticVOList) {
                            ShortCountLog countryStatistic = new ShortCountLog();
                            countryStatistic.setType(3l);
                            countryStatistic.setUserId(userId);
                            countryStatistic.setCountryName(countryStatisticVO.getCountryName());
                            countryStatistic.setUserCount(countryStatisticVO.getUserCount());
                            countryStatistic.setValue(countryStatisticVO.getValue());
                            shortCountLogService.insertShortCountLog(countryStatistic);
                        }

                        List<CountryStatisticVO> avgPayTopCountries = shortOrderMapper.avgPayTopCountries(orderDTO, userId);
                        for (CountryStatisticVO avgPayTopCountry : avgPayTopCountries) {
                            ShortCountLog avgPayTopCountryStatistic = new ShortCountLog();
                            avgPayTopCountryStatistic.setType(4l);
                            avgPayTopCountryStatistic.setUserId(userId);
                            avgPayTopCountryStatistic.setCountryName(avgPayTopCountry.getCountryName());
                            avgPayTopCountryStatistic.setUserCount(avgPayTopCountry.getUserCount());
                            avgPayTopCountryStatistic.setValue(avgPayTopCountry.getValue());
                            shortCountLogService.insertShortCountLog(avgPayTopCountryStatistic);
                        }

                        List<CountryStatisticVO> subscribeRatioTopCountries =shortOrderMapper.subscribeRatioTopCountries(orderDTO, userId);
                        for (CountryStatisticVO subscribeRatioTopCountry : subscribeRatioTopCountries) {
                            ShortCountLog subscribeRatioTopCountryStatistic = new ShortCountLog();
                            subscribeRatioTopCountryStatistic.setType(5l);
                            subscribeRatioTopCountryStatistic.setUserId(userId);
                            subscribeRatioTopCountryStatistic.setCountryName(subscribeRatioTopCountry.getCountryName());
                            subscribeRatioTopCountryStatistic.setUserCount(subscribeRatioTopCountry.getUserCount());
                            subscribeRatioTopCountryStatistic.setValue(subscribeRatioTopCountry.getValue());
                            shortCountLogService.insertShortCountLog(subscribeRatioTopCountryStatistic);
                        }

                        List<CountryStatisticVO> retentionRateTopCountries =shortOrderMapper.retentionRateTopCountries(orderDTO, userId);
                        for (CountryStatisticVO retentionRateTopCountry : retentionRateTopCountries) {
                            ShortCountLog retentionRateTopCountryStatistic = new ShortCountLog();
                            retentionRateTopCountryStatistic.setType(6l);
                            retentionRateTopCountryStatistic.setUserId(userId);
                            retentionRateTopCountryStatistic.setCountryName(retentionRateTopCountry.getCountryName());
                            retentionRateTopCountryStatistic.setUserCount(retentionRateTopCountry.getUserCount());
                            retentionRateTopCountryStatistic.setValue(retentionRateTopCountry.getValue());
                            shortCountLogService.insertShortCountLog(retentionRateTopCountryStatistic);
                        }

                        List<CountryStatisticVO> userRateTopCountries=shortOrderMapper.userRateTopCountries(orderDTO, userId);
                        for (CountryStatisticVO userRateTopCountry : userRateTopCountries) {
                            ShortCountLog userRateTopCountryStatistic = new ShortCountLog();
                            userRateTopCountryStatistic.setType(2l);
                            userRateTopCountryStatistic.setUserId(userId);
                            userRateTopCountryStatistic.setCountryName(userRateTopCountry.getCountryName());
                            userRateTopCountryStatistic.setUserCount(userRateTopCountry.getUserCount());
                            userRateTopCountryStatistic.setValue(userRateTopCountry.getValue());
                            shortCountLogService.insertShortCountLog(userRateTopCountryStatistic);
                        }
                    }
                }

                shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
                return AjaxResult.success("操作成功=");
            }
            shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
            return AjaxResult.success("操作成功");

        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("status", "error");
            error.put("message", "Validation failed: " + e.getMessage());
            shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
            return AjaxResult.error("操作失败");
        }
    }

    @Override
    public ShortCountLog getCountList(ShortOrderDTO orderDTO) {
        Long loggedInUserId = SecurityUtils.getUserId();
//        Long filterUserId = (loggedInUserId != null && loggedInUserId == 1) ? null : loggedInUserId;

//        formatDateParams(orderDTO);
        ShortCountLog result = shortCountLogService.getByTypeAndUserId(loggedInUserId, 1L);
        if (result == null) {
            result = new ShortCountLog();
            ShortOrderDashboardVO shortOrderDashboardVO = dashboardStatistics(orderDTO);
            if (shortOrderDashboardVO == null) {
                return new ShortCountLog();
            }
            result.setPayRateTopCountries(shortOrderDashboardVO.getPayRateTopCountries());
            result.setAvgPayTopCountries(shortOrderDashboardVO.getAvgPayTopCountries());
            result.setSubscribeRatioTopCountries(shortOrderDashboardVO.getSubscribeRatioTopCountries());
            result.setRetentionRateTopCountries(shortOrderDashboardVO.getRetentionRateTopCountries());
            result.setUserRateTopCountries(shortOrderDashboardVO.getUserRateTopCountries());
            BeanUtils.copyProperties(shortOrderDashboardVO, result);
            return result;
        }


//        List<CountryStatisticVO> list = shortCountLogService.getByTypeAndUserIdList(filterUserId,3l);


        result.setPayRateTopCountries(shortCountLogService.getByTypeAndUserIdList(loggedInUserId, 3L));
        result.setAvgPayTopCountries(shortCountLogService.getByTypeAndUserIdList(loggedInUserId, 4L));
        result.setSubscribeRatioTopCountries(shortCountLogService.getByTypeAndUserIdList(loggedInUserId, 5L));
        result.setRetentionRateTopCountries(shortCountLogService.getByTypeAndUserIdList(loggedInUserId, 6L));
        result.setUserRateTopCountries(shortCountLogService.getByTypeAndUserIdList(loggedInUserId, 2L));
        return result;
    }

    @Override
    public AjaxResult countOrderLog(String publicKey) {
        int count = shortIntermedStatusService.countByName("countOrderLog");
        if(count>0)
            return AjaxResult.success("自动统计订单页数据，请勿重复操作");

        ShortIntermedStatus shortIntermedStatus = new ShortIntermedStatus();
        shortIntermedStatus.setName("countOrderLog");
        shortIntermedStatusService.insertShortIntermedStatus(shortIntermedStatus);


        try {
            String privateKey = "-----BEGIN OPENSSH PRIVATE KEY-----\n" +
                    "b3BlbnNzaC1rZXktdjEAAAAABG5vbmUAAAAEbm9uZQAAAAAAAAABAAAAMwAAAAtzc2gtZW\n" +
                    "QyNTUxOQAAACDdx+eInOus2G8pGSS+paM8QV5SkQdAXGh/e0Z7pRaDRwAAAJiIp66uiKeu\n" +
                    "rgAAAAtzc2gtZWQyNTUxOQAAACDdx+eInOus2G8pGSS+paM8QV5SkQdAXGh/e0Z7pRaDRw\n" +
                    "AAAEDJW6rFB140gGyRJZM+mqoIFj8HAGrGpnuWj+sCJa/06N3H54ic66zYbykZJL6lozxB\n" +
                    "XlKRB0BcaH97RnulFoNHAAAAE2Zlbmd4aW41MjIwQDE2My5jb20BAg==\n" +
                    "-----END OPENSSH PRIVATE KEY-----";

            // 2. 加载密钥对
            KeyPair keyPair = KeyPair.load(new JSch(), privateKey.getBytes(), null);
            byte[] derivedPublicKeyBlob = keyPair.getPublicKeyBlob();

            // 3. 解析用户公钥（按空格分割并解码Base64）
            String[] publicKeyParts = publicKey.split(" ");
            if (publicKeyParts.length < 2) {
                throw new IllegalArgumentException("Invalid public key format");
            }
            byte[] userPublicKeyBlob = Base64.getDecoder().decode(publicKeyParts[1]);

            // 4. 安全比对（避免时序攻击）
            boolean isMatch = MessageDigest.isEqual(derivedPublicKeyBlob, userPublicKeyBlob);
            if(isMatch){
                log.info("验证密钥正确------订单页统计定时任务");

                List<Long> userIds = shortIntermedStatusService.getSysUserId();
                for (Long userId : userIds) {
                    ShortOrderDTO orderDTO = new ShortOrderDTO();
                    formatDateParams(orderDTO);

                    ShortOrderDashboardVO result = shortOrderMapper.dailyStatisticsOld(orderDTO, userId);
                    if (null != result) {

                        log.info("获取深链用户数据");
                        ShortUserForLinkVO vo = shortUserMapper.getSystemAddUserCount(orderDTO);
                        log.info("重新计算支付率、ios支付率、安卓支付率");
                        result.setPayRate(BigDecimalUtil(BigDecimal.valueOf(result.getPayUserCount()), BigDecimal.valueOf(vo.getLinkUser())));
                        result.setIosPayRate(BigDecimalUtil(BigDecimal.valueOf(result.getIosPayUserCount()), BigDecimal.valueOf(vo.getIosUser())));
                        result.setAndroidPayRate(BigDecimalUtil(BigDecimal.valueOf(result.getAndroidPayUserCount()), BigDecimal.valueOf(vo.getAndroidUser())));


                        ShortCountLog shortCountLog = new ShortCountLog();
                        shortCountLog.setType(7l);
                        shortCountLog.setUserId(userId);
                        BeanUtils.copyProperties(result, shortCountLog); // 同名属性自动拷贝

                        shortCountLogService.insertShortCountLog(shortCountLog);

                    }
                }

                shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
                return AjaxResult.success("操作成功=");
            }
            shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
            return AjaxResult.success("操作成功");

        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("status", "error");
            error.put("message", "Validation failed: " + e.getMessage());
            shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
            return AjaxResult.error("操作失败");
        }
    }


    @Override
    public ShortCountLog getCountOrderList(ShortOrderDTO orderDTO) {
        Long loggedInUserId = SecurityUtils.getUserId();
//        Long filterUserId = (loggedInUserId != null && loggedInUserId == 1) ? null : loggedInUserId;

//        formatDateParams(orderDTO);
        ShortCountLog result = shortCountLogService.getByTypeAndUserId(loggedInUserId, 7L);
        if (result == null) {
            result = new ShortCountLog();

            ShortOrderDailyVO dailyStatisticsvo = dailyStatistics(orderDTO);

            if (dailyStatisticsvo == null) {
                return new ShortCountLog();
            }

            BeanUtils.copyProperties(dailyStatisticsvo, result);
            return result;
        }
        ShortUserForLinkVO vo = shortUserMapper.getSystemAddUserCount(orderDTO);
        result.setShortUserForLinkVO(vo);
        return result;
    }
}
