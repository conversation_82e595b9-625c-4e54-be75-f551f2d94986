package com.ruoyi.dto;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;
import java.util.Objects;

/**
 * 邮件发送记录对象 short_email_send_log
 *
 * <AUTHOR>
 * @date 2025-05-30
 */
@Data
@ApiModel("邮件发送记录对象")
public class ShortEmailSendLogDTO {

    @ApiModelProperty("分页大小")
    private Integer pageSize;
    @ApiModelProperty("分页页数")
    private Integer pageNum;
    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("已经发送次数")
    private Long num;

    @ApiModelProperty("邮箱")
    private String email;
    @ApiModelProperty("订单id")
    private Long orderId;
    @ApiModelProperty("链接ID")
    private Long linkId;
    @ApiModelProperty("链接名称")
    private String linkName;
    @ApiModelProperty("支付状态")
    private String status;
    @ApiModelProperty("结束时间")
    private String endDate;
    @ApiModelProperty("开始时间")
    private String startDate;

    public Integer getPageSize() {
        if(Objects.isNull(pageSize)){
            return 10;
        }
        return pageSize;
    }



    public Integer getPageNum() {
        if(Objects.isNull(pageNum)){
            return 0;
        }
        return pageNum;
    }


}
