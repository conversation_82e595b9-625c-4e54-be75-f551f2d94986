package com.ruoyi.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.util.Date;

/**
 * 邮箱数据统计
 * @TableName short_email_data
 */
@ApiModel("邮箱发送数据看板")
@Data
public class ShortEmailDataVO {
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 亚马逊发送的次数
     */
    @ApiModelProperty("亚马逊发送的次数")
    private Integer awsSendNum;

    /**
     * brevo发送次数
     */
    @ApiModelProperty("brevo发送次数")
    private Integer brevoSendNum;

    /**
     * 支付成功数量
     */
    @ApiModelProperty("支付成功数量")
    private Integer payNum;

    /**
     * 当天支付成功数
     */
    @ApiModelProperty("当天支付成功数")
    private Integer dayPayNum;

    /**
     * 
     */
    @ApiModelProperty("当日aws发送次数")
    private Integer dayAwsSendNum;

    /**
     * 
     */
    @ApiModelProperty("当日brevo发送次数")
    private Integer dayBrevoSendNum;

    /**
     * 邮件触达用户
     */
    @ApiModelProperty("邮件触达用户")
    private Integer sendUser;

    /**
     * 当天触达
     */
    @ApiModelProperty("当天触达")
    private Integer daySendUser;

    /**
     * 当天aws进入页面
     */
    @ApiModelProperty("当天aws进入页面")
    private Integer dayAwsEnterNum;

    /**
     * aws进入页面
     */
    @ApiModelProperty("aws进入页面")
    private Integer awsEnterNum;

    /**
     * 当天brveo 进入页面
     */
    @ApiModelProperty("当天brevo 进入页面")
    private Integer dayBrevoEnterNum;

    /**
     * brveo进入页面次数
     */
    @ApiModelProperty("brveo进入页面次数")
    private Integer brveoEnterNum;

    /**
     * 时间
     */
    @ApiModelProperty("时间")
    private LocalDate dayNode;

    /**
     * 当天用户进入次数
     */
    @ApiModelProperty("当天用户进入次数")
    private Integer dayUserEnter;

    /**
     * 用户进入次数
     */
    @ApiModelProperty("用户进入次数")
    private Integer userEnter;

}