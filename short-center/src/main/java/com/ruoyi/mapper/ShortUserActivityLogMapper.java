package com.ruoyi.mapper;

import com.ruoyi.domain.ShortUserActivityLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户行为记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
public interface ShortUserActivityLogMapper
{
    /**
     * 查询用户行为记录
     *
     * @param id 用户行为记录主键
     * @return 用户行为记录
     */
    public ShortUserActivityLog selectShortUserActivityLogById(Long id);

    /**
     * 查询用户行为记录列表
     *
     * @param shortUserActivityLog 用户行为记录
     * @return 用户行为记录集合
     */
    public List<ShortUserActivityLog> selectShortUserActivityLogList(ShortUserActivityLog shortUserActivityLog);

    /**
     * 新增用户行为记录
     *
     * @param shortUserActivityLog 用户行为记录
     * @return 结果
     */
    public int insertShortUserActivityLog(ShortUserActivityLog shortUserActivityLog);

    /**
     * 修改用户行为记录
     *
     * @param shortUserActivityLog 用户行为记录
     * @return 结果
     */
    public int updateShortUserActivityLog(ShortUserActivityLog shortUserActivityLog);

    /**
     * 删除用户行为记录
     *
     * @param id 用户行为记录主键
     * @return 结果
     */
    public int deleteShortUserActivityLogById(Long id);

    /**
     * 批量删除用户行为记录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteShortUserActivityLogByIds(Long[] ids);

    List<ShortUserActivityLog> getInfoByUserId(Long userId);

    /**
     * 批量插入用户行为记录
     * @param list
     */
    void insertBatchShortUserActivityLog(List<ShortUserActivityLog> list);

    ShortUserActivityLog getEntUrlByUserId(Long userId);

    ShortUserActivityLog getNoteByUserId(Long userId);

    List<ShortUserActivityLog> getNoteByUserIdLimit3(@Param("userId") Long userId,@Param("note") String note);
}
