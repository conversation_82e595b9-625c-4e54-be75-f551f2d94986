package com.ruoyi.mapper;

import com.ruoyi.domain.ShortApp;

import java.util.List;

/**
 * App管理Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
public interface ShortAppMapper
{
    /**
     * 查询App管理
     *
     * @param id App管理主键
     * @return App管理
     */
    public ShortApp selectShortAppById(Long id);

    /**
     * 查询App管理列表
     *
     * @param shortApp App管理
     * @return App管理集合
     */
    public List<ShortApp> selectShortAppList(ShortApp shortApp);

    /**
     * 新增App管理
     *
     * @param shortApp App管理
     * @return 结果
     */
    public int insertShortApp(ShortApp shortApp);

    /**
     * 修改App管理
     *
     * @param shortApp App管理
     * @return 结果
     */
    public int updateShortApp(ShortApp shortApp);

    /**
     * 删除App管理
     *
     * @param id App管理主键
     * @return 结果
     */
    public int deleteShortAppById(Long id);

    /**
     * 批量删除App管理
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteShortAppByIds(Long[] ids);

    /**
     * 根据appIds查询app信息
     * @param list
     * @return
     */
    List<ShortApp> selectShortAppByIds(List<Long> list);
}
