package com.ruoyi.mapper;

import java.util.List;
import com.ruoyi.domain.ShortRunlog;

/**
 * 日志Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
public interface ShortRunlogMapper 
{
    /**
     * 查询日志
     * 
     * @param id 日志主键
     * @return 日志
     */
    public ShortRunlog selectShortRunlogById(Long id);

    /**
     * 查询日志列表
     * 
     * @param shortRunlog 日志
     * @return 日志集合
     */
    public List<ShortRunlog> selectShortRunlogList(ShortRunlog shortRunlog);

    /**
     * 新增日志
     * 
     * @param shortRunlog 日志
     * @return 结果
     */
    public int insertShortRunlog(ShortRunlog shortRunlog);

    /**
     * 修改日志
     * 
     * @param shortRunlog 日志
     * @return 结果
     */
    public int updateShortRunlog(ShortRunlog shortRunlog);

    /**
     * 删除日志
     * 
     * @param id 日志主键
     * @return 结果
     */
    public int deleteShortRunlogById(Long id);

    /**
     * 批量删除日志
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteShortRunlogByIds(Long[] ids);
}
