package com.ruoyi.mapper;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.domain.ShortEmailSendLog;
import com.ruoyi.dto.ShortEmailSendLogDTO;
import com.ruoyi.vo.ShortEmailSendLogVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 邮件发送记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-30
 */
public interface ShortEmailSendLogMapper extends BaseMapper<ShortEmailSendLog>
{
    /**
     * 查询邮件发送记录
     *
     * @param id 邮件发送记录主键
     * @return 邮件发送记录
     */
    public ShortEmailSendLog selectShortEmailSendLogById(Long id);

    /**
     * 查询邮件发送记录列表
     *
     * @param shortEmailSendLog 邮件发送记录
     * @return 邮件发送记录集合
     */
    public List<ShortEmailSendLog> selectShortEmailSendLogList(ShortEmailSendLog shortEmailSendLog);

    /**
     * 新增邮件发送记录
     *
     * @param shortEmailSendLog 邮件发送记录
     * @return 结果
     */
    public int insertShortEmailSendLog(ShortEmailSendLog shortEmailSendLog);

    /**
     * 修改邮件发送记录
     *
     * @param shortEmailSendLog 邮件发送记录
     * @return 结果
     */
    public int updateShortEmailSendLog(ShortEmailSendLog shortEmailSendLog);
    public int updateStatusByUserId(ShortEmailSendLog shortEmailSendLog);
    /**
     * 删除邮件发送记录
     *
     * @param id 邮件发送记录主键
     * @return 结果
     */
    public int deleteShortEmailSendLogById(Long id);

    /**
     * 批量删除邮件发送记录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteShortEmailSendLogByIds(Long[] ids);

    List<ShortEmailSendLog> getListByEndTime();


    Page<ShortEmailSendLogVO> emailList(@Param("page")Page<ShortEmailSendLogVO> page, @Param("sendLogDTO")ShortEmailSendLogDTO sendLogDTO);
    ShortEmailSendLog getByEmail(String email);

    void updateBatch(@Param( "list")List<Long> logIds);
}
