package com.ruoyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.common.core.domain.KvEntity;
import com.ruoyi.domain.ShortOrder;
import com.ruoyi.domain.ShortRenewSubscribeData;
import com.ruoyi.dto.ShortOrderDTO;
import com.ruoyi.dto.ShortSubscribeDataDTO;
import com.ruoyi.vo.*;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.List;


/**
 * 订单表Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
public interface ShortOrderMapper extends BaseMapper<ShortOrder> {
    /**
     * 查询订单表
     *
     * @param id 订单表主键
     * @return 订单表
     */
    public ShortOrder selectShortOrderById(Long id);

    /**
     * 查询订单表列表
     *
     * @param shortOrder 订单表
     * @return 订单表集合
     */
    public List<ShortOrder> selectShortOrderList(ShortOrder shortOrder);

    /**
     * 新增订单表
     *
     * @param shortOrder 订单表
     * @return 结果
     */
    public int insertShortOrder(ShortOrder shortOrder);

    /**
     * 修改订单表
     *
     * @param shortOrder 订单表
     * @return 结果
     */
    public int updateShortOrder(ShortOrder shortOrder);

    /**
     * 删除订单表
     *
     * @param id 订单表主键
     * @return 结果
     */
    public int deleteShortOrderById(Long id);

    /**
     * 批量删除订单表
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteShortOrderByIds(Long[] ids);

    /**
     * 根据用户id查询订单
     *
     * @param list
     * @return
     */
    List<ShortOrder> selectByUserIdList(List<Long> list);


    ShortOrderOverViewVO orderOverView();

    List<KvEntity> payTopCountry();

    ShortOrderStatisticVO orderStatisticView(@Param("dto") ShortOrderDTO orderDTO);

    List<ShortOrderVO> orderPageList(@Param("dto") ShortOrderDTO orderDTO, @Param("loggedInUserId") Long filterUserId);

    List<ShortOrder> selectByPaymentIntentId(String pid);

    ShortOrder findFirstByUserAndPay_typeAndStatusOrderByUpdatedAtDesc(@Param("userId") Long userId, @Param("payType") String payType, @Param("status") String status);

    /**
     * 获取首页统计数据
     *
     * @param dto            查询条件
     * @param loggedInUserId 当前登录用户ID
     * @return 首页统计数据
     */
    ShortOrderDashboardVO dashboardStatistics(@Param("dto") ShortOrderDTO dto, @Param("loggedInUserId") Long loggedInUserId);

    /**
     * 获取上期订阅用户数
     *
     * @param dto            查询条件
     * @param loggedInUserId 当前登录用户ID
     * @return 上期订阅用户数
     */
    Long getLastPeriodSubscribeUserCount(@Param("dto") ShortOrderDTO dto, @Param("loggedInUserId") Long loggedInUserId);

    /**
     * 获取支付率Top20国家
     *
     * @param dto            查询条件
     * @param loggedInUserId 当前登录用户ID
     * @return 国家支付率列表
     */
    List<CountryStatisticVO> payRateTopCountries(@Param("dto") ShortOrderDTO dto, @Param("loggedInUserId") Long loggedInUserId);

    /**
     * 获取付费人均支付Top20国家
     *
     * @param dto            查询条件
     * @param loggedInUserId 当前登录用户ID
     * @return 国家人均支付列表
     */
    List<CountryStatisticVO> avgPayTopCountries(@Param("dto") ShortOrderDTO dto, @Param("loggedInUserId") Long loggedInUserId);

    /**
     * 获取订阅收入占比Top20国家
     *
     * @param dto            查询条件
     * @param loggedInUserId 当前登录用户ID
     * @return 国家订阅收入占比列表
     */
    List<CountryStatisticVO> subscribeRatioTopCountries(@Param("dto") ShortOrderDTO dto, @Param("loggedInUserId") Long loggedInUserId);

    /**
     * 获取订阅留存率Top20国家
     *
     * @param dto            查询条件
     * @param loggedInUserId 当前登录用户ID
     * @return 国家订阅留存率列表
     */
    List<CountryStatisticVO> retentionRateTopCountries(@Param("dto") ShortOrderDTO dto, @Param("loggedInUserId") Long loggedInUserId);

    /**
     * 获取订单表数据统计
     *
     * @param dto            查询条件
     * @param loggedInUserId 当前登录用户ID
     * @return 订单表统计数据
     */
    ShortOrderDailyVO dailyStatistics(@Param("dto") ShortOrderDTO dto, @Param("loggedInUserId") Long loggedInUserId);

    String selectNewByUserId(Long userId);

    ShortSubscribeDataDTO getSubscribeDataByDate(@Param("date") String date);

    List<ShortRenewSubscribeData> getRenewSubscribeDataByDate(@Param("date") String date);

    List<ShortRenewSubscribeData> getFirstSubscribeDataByDate(@Param("date") String date);

    List<ShortRenewSubscribeData> findRenewSubscribeDataByUserId(@Param("userId") Long userId,@Param("date") DateTimeFormatter date);

    BigDecimal getOldUserRenewAmount(@Param("dto") ShortOrderDTO dto, @Param("loggedInUserId") Long loggedInUserId, @Param("date") String date);

    BigDecimal getOldUserRenewAmountByStartDate(@Param("dto") ShortOrderDTO orderDTO, @Param("loggedInUserId")  Long filterUserId);

    List<CountryStatisticVO> userRateTopCountries(@Param("dto")ShortOrderDTO orderDTO, @Param("loggedInUserId") Long filterUserId);

    /**
     * 查询用户针对特定Pixel ID，在指定时间点前（含）的成功订单数量
     *
     * @param userId 用户ID
     * @param pixelId 像素ID
     * @param orderTime 时间点
     * @param currentOrderId 当前订单ID，用于排除自身
     * @return 成功订单数量
     */
    Integer countSuccessfulOrdersByPixelIdBeforeTime(@Param("userId") Long userId, @Param("pixelId") Long pixelId, @Param("orderTime") Date orderTime, @Param("currentOrderId") Long currentOrderId);

    List<ShortOrder> selectListLikeProductId(ShortOrder queryOrder);

    ShortOrderDailyVO dailyStatisticsReal(@Param("dto") ShortOrderDTO dto, @Param("loggedInUserId") Long loggedInUserId);

    ShortOrderDashboardVO dailyStatisticsOld(@Param("dto") ShortOrderDTO dto, @Param("loggedInUserId") Long loggedInUserId);
}
