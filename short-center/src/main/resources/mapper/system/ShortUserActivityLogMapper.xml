<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.ShortUserActivityLogMapper">

    <resultMap type="ShortUserActivityLog" id="ShortUserActivityLogResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="appId"    column="app_id"    />
        <result property="state"    column="state"    />
        <result property="content"    column="content"    />
        <result property="note"    column="note"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectShortUserActivityLogVo">
        select id, user_id, app_id, state, content, note, status, create_by, create_time, update_by, update_time from short_user_activity_log
    </sql>

    <select id="selectShortUserActivityLogList" parameterType="ShortUserActivityLog" resultMap="ShortUserActivityLogResult">
        SELECT
        sual.id,
        sual.user_id,
        sual.app_id,
        sual.state,
        sual.content,
        sual.note,
        sual.STATUS,
        sual.create_by,
        sual.create_time,
        sual.update_by,
        sual.update_time,
        sa.`name` appName,
        su.username userName
        FROM
        short_user_activity_log sual
        LEFT JOIN short_app sa on sual.app_id =sa.id
        LEFT JOIN short_user su on su.id = sual.user_id
        <where>
            <if test="startTime != null and endTime != null">
                sual.create_time >= #{startTime}
                AND sual.create_time &lt; #{endTime}
            </if>
            <if test="userId != null "> and sual.user_id = #{userId}</if>
            <if test="appId != null "> and sual.app_id = #{appId}</if>
            <if test="state != null  and state != ''"> and sual.state = #{state}</if>
            <if test="content != null  and content != ''"> and sual.content = #{content}</if>
            <if test="note != null  and note != ''"> and sual.note = #{note}</if>
            <if test="status != null  and status != ''"> and sual.status = #{status}</if>
        </where>
        ORDER BY sual.id DESC
    </select>

    <select id="selectShortUserActivityLogById" parameterType="Long" resultMap="ShortUserActivityLogResult">
        <include refid="selectShortUserActivityLogVo"/>
        where id = #{id}
    </select>
    <select id="getInfoByUserId" resultType="com.ruoyi.domain.ShortUserActivityLog">
        SELECT
            sual.*,sa.`name` appName,
            su.username userName
        FROM
            short_user_activity_log sual
                LEFT JOIN short_app sa on sual.app_id =sa.id
                LEFT JOIN short_user su on su.id = sual.user_id
        WHERE
            sual.user_id = #{userId}
    </select>
    <select id="getEntUrlByUserId" resultType="com.ruoyi.domain.ShortUserActivityLog">
        SELECT * from short_user_activity_log WHERE content like '%进入Url：%' and user_id = #{userId} ORDER BY create_time desc  LIMIT 1

    </select>
    <select id="getNoteByUserId" resultType="com.ruoyi.domain.ShortUserActivityLog">
        SELECT * from short_user_activity_log WHERE content like '%影视ID%' and user_id = #{userId} and note is not null ORDER BY create_time desc  LIMIT 1
    </select>
    <select id="getNoteByUserIdLimit3" resultType="com.ruoyi.domain.ShortUserActivityLog">
        SELECT
            ANY_VALUE(id) id,
            ANY_VALUE(user_id) user_id,
            ANY_VALUE(app_id) app_id,
            ANY_VALUE(state) state,
            ANY_VALUE(content) content,
            note,
            ANY_VALUE(STATUS) STATUS,
            ANY_VALUE(create_by) create_by,
            ANY_VALUE(create_time) create_time,
            ANY_VALUE(update_by) update_by,
            ANY_VALUE(update_time) update_time
        FROM
            short_user_activity_log
         WHERE content LIKE '%影视ID%' AND user_id = #{userId} AND note IS NOT NULL and note !=#{note} AND content LIKE '%video/?%'
        GROUP BY note ORDER BY create_time desc LIMIT 3
    </select>


    <insert id="insertShortUserActivityLog" parameterType="ShortUserActivityLog" useGeneratedKeys="true" keyProperty="id">
        insert into short_user_activity_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="appId != null">app_id,</if>
            <if test="state != null">state,</if>
            <if test="content != null">content,</if>
            <if test="note != null">note,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="appId != null">#{appId},</if>
            <if test="state != null">#{state},</if>
            <if test="content != null">#{content},</if>
            <if test="note != null">#{note},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>
    <insert id="insertBatchShortUserActivityLog">
        insert into short_user_activity_log (user_id,app_id,state,content,note,status,create_by,create_time,update_by,update_time) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.userId},#{item.appId},#{item.state},#{item.content},#{item.note},#{item.status},#{item.createBy},#{item.createTime},#{item.updateBy},#{item.updateTime})
        </foreach>
    </insert>

    <update id="updateShortUserActivityLog" parameterType="ShortUserActivityLog">
        update short_user_activity_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="appId != null">app_id = #{appId},</if>
            <if test="state != null">state = #{state},</if>
            <if test="content != null">content = #{content},</if>
            <if test="note != null">note = #{note},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteShortUserActivityLogById" parameterType="Long">
        delete from short_user_activity_log where id = #{id}
    </delete>

    <delete id="deleteShortUserActivityLogByIds" parameterType="String">
        delete from short_user_activity_log where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
