<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.ShortExtplatsMapper">
    
    <resultMap type="ShortExtplats" id="ShortExtplatsResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="token"    column="token"    />
        <result property="key"    column="key"    />
        <result property="clientId"    column="client_id"    />
        <result property="clientSecret"    column="client_secret"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectShortExtplatsVo">
        select id, name, token, `key`, client_id, client_secret, status, create_by, create_time, update_by, update_time, remark from short_extplats
    </sql>

    <select id="selectShortExtplatsList" parameterType="ShortExtplats" resultMap="ShortExtplatsResult">
        <include refid="selectShortExtplatsVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="token != null  and token != ''"> and token = #{token}</if>
            <if test="key != null  and key != ''"> and `key` = #{key}</if>
            <if test="clientId != null  and clientId != ''"> and client_id = #{clientId}</if>
            <if test="clientSecret != null  and clientSecret != ''"> and client_secret = #{clientSecret}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectShortExtplatsById" parameterType="Long" resultMap="ShortExtplatsResult">
        <include refid="selectShortExtplatsVo"/>
        where id = #{id}
    </select>

    <insert id="insertShortExtplats" parameterType="ShortExtplats" useGeneratedKeys="true" keyProperty="id">
        insert into short_extplats
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="token != null">token,</if>
            <if test="key != null">`key`,</if>
            <if test="clientId != null">client_id,</if>
            <if test="clientSecret != null">client_secret,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="token != null">#{token},</if>
            <if test="key != null">#{key},</if>
            <if test="clientId != null">#{clientId},</if>
            <if test="clientSecret != null">#{clientSecret},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateShortExtplats" parameterType="ShortExtplats">
        update short_extplats
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="token != null">token = #{token},</if>
            <if test="key != null">`key` = #{key},</if>
            <if test="clientId != null">client_id = #{clientId},</if>
            <if test="clientSecret != null">client_secret = #{clientSecret},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteShortExtplatsById" parameterType="Long">
        delete from short_extplats where id = #{id}
    </delete>

    <delete id="deleteShortExtplatsByIds" parameterType="String">
        delete from short_extplats where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>