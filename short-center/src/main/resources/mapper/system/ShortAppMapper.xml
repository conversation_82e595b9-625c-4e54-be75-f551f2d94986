<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.ShortAppMapper">

    <resultMap type="ShortApp" id="ShortAppResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="icon"    column="icon"    />
        <result property="deepLink"    column="deep_link"    />
        <result property="adjustFacebookToken"    column="adjust_facebook_token"    />
        <result property="facebookPixelId"    column="facebook_pixel_id"    />
        <result property="facebookAccessToken"    column="facebook_access_token"    />
        <result property="company"    column="company"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="type"    column="type"    />
        <result property="extplatsId"    column="extplats_id"    />
        <result property="afLinkUrl"    column="af_link_url"    />
        <result property="publishStatus" column="publish_status"/>
        <result property="appVersion"    column="app_version"    />

    </resultMap>

    <sql id="selectShortAppVo">
        select id, name, icon, deep_link, adjust_facebook_token, facebook_pixel_id, facebook_access_token, company, status, create_by, create_time, update_by, update_time, remark, type, extplats_id,af_link_url,  publish_status, app_version from short_app
    </sql>

    <select id="selectShortAppList" parameterType="ShortApp" resultMap="ShortAppResult">
        <include refid="selectShortAppVo"/>
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="icon != null  and icon != ''"> and icon = #{icon}</if>
            <if test="deepLink != null  and deepLink != ''"> and deep_link = #{deepLink}</if>
            <if test="adjustFacebookToken != null  and adjustFacebookToken != ''"> and adjust_facebook_token = #{adjustFacebookToken}</if>
            <if test="facebookPixelId != null  and facebookPixelId != ''"> and facebook_pixel_id = #{facebookPixelId}</if>
            <if test="facebookAccessToken != null  and facebookAccessToken != ''"> and facebook_access_token = #{facebookAccessToken}</if>
            <if test="company != null  and company != ''"> and company like concat('%', #{company}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="extplatsId != null"> and extplats_id = #{extplatsId}</if>
            <if test="afLinkUrl != null"> and af_link_url = #{afLinkUrl}</if>
             <if test="publishStatus != null"> and publish_status = #{publishStatus}</if>
            <if test="appVersion != null"> and app_version = #{appVersion}</if>
        </where>
    </select>

    <select id="selectShortAppById" parameterType="Long" resultMap="ShortAppResult">
        <include refid="selectShortAppVo"/>
        where id = #{id}
    </select>
    <select id="selectShortAppByIds" resultMap="ShortAppResult">
        <include refid="selectShortAppVo"/>
        where id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertShortApp" parameterType="ShortApp" useGeneratedKeys="true" keyProperty="id">
        insert into short_app
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="icon != null">icon,</if>
            <if test="deepLink != null">deep_link,</if>
            <if test="adjustFacebookToken != null">adjust_facebook_token,</if>
            <if test="facebookPixelId != null">facebook_pixel_id,</if>
            <if test="facebookAccessToken != null">facebook_access_token,</if>
            <if test="company != null">company,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="type != null">type,</if>
            <if test="extplatsId != null">extplats_id,</if>
            <if test="afLinkUrl != null">af_link_url,</if>
            <if test="publishStatus != null">publish_status,</if>
            <if test="appVersion != null">app_version,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="icon != null">#{icon},</if>
            <if test="deepLink != null">#{deepLink},</if>
            <if test="adjustFacebookToken != null">#{adjustFacebookToken},</if>
            <if test="facebookPixelId != null">#{facebookPixelId},</if>
            <if test="facebookAccessToken != null">#{facebookAccessToken},</if>
            <if test="company != null">#{company},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="type != null">#{type},</if>
            <if test="extplatsId != null">#{extplatsId},</if>
            <if test="afLinkUrl != null">#{afLinkUrl},</if>
            <if test="publishStatus != null">#{publishStatus},</if>
            <if test="appVersion != null">#{appVersion},</if>
         </trim>
    </insert>

    <update id="updateShortApp" parameterType="ShortApp">
        update short_app
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="icon != null">icon = #{icon},</if>
            <if test="deepLink != null">deep_link = #{deepLink},</if>
            <if test="adjustFacebookToken != null">adjust_facebook_token = #{adjustFacebookToken},</if>
            <if test="facebookPixelId != null">facebook_pixel_id = #{facebookPixelId},</if>
            <if test="facebookAccessToken != null">facebook_access_token = #{facebookAccessToken},</if>
            <if test="company != null">company = #{company},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="type != null">type = #{type},</if>
            <if test="extplatsId != null">extplats_id = #{extplatsId},</if>
            <if test="afLinkUrl != null">af_link_url = #{afLinkUrl},</if>
            <if test="publishStatus != null">publish_status = #{publishStatus},</if>
            <if test="appVersion != null">app_version = #{appVersion},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteShortAppById" parameterType="Long">
        delete from short_app where id = #{id}
    </delete>

    <delete id="deleteShortAppByIds" parameterType="String">
        delete from short_app where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
