<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.ShortEmailDataMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.domain.ShortEmailData">
            <id property="id" column="id" />
            <result property="awsSendNum" column="aws_send_num" />
            <result property="brevoSendNum" column="brevo_send_num" />
            <result property="payNum" column="pay_num" />
            <result property="dayPayNum" column="day_pay_num" />
            <result property="dayAwsSendNum" column="day_aws_send_num" />
            <result property="dayBrevoSendNum" column="day_brevo_send_num" />
            <result property="sendUser" column="send_user" />
            <result property="daySendUser" column="day_send_user" />
            <result property="dayAwsEnterNum" column="day_aws_enter_num" />
            <result property="awsEnterNum" column="aws_enter_num" />
            <result property="dayBrevoEnterNum" column="day_brevo_enter_num" />
            <result property="brveoEnterNum" column="brveo_enter_num" />
            <result property="dayNode" column="day_node" />
            <result property="dayUserEnter" column="day_user_enter" />
            <result property="userEnter" column="user_enter" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,aws_send_num,brevo_send_num,pay_num,day_pay_num,day_aws_send_num,
        day_brevo_send_num,send_user,day_send_user,day_aws_enter_num,aws_enter_num,
        day_brevo_enter_num,brveo_enter_num,day_node,day_user_enter,user_enter,
        create_time,update_time
    </sql>
</mapper>
