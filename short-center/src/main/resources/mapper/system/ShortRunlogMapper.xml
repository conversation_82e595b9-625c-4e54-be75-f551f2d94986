<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.ShortRunlogMapper">
    
    <resultMap type="ShortRunlog" id="ShortRunlogResult">
        <result property="id"    column="id"    />
        <result property="type"    column="type"    />
        <result property="state"    column="state"    />
        <result property="content"    column="content"    />
        <result property="note"    column="note"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectShortRunlogVo">
        select id, type, state, content, note, status, create_by, create_time, update_by, update_time from short_runlog
    </sql>

    <select id="selectShortRunlogList" parameterType="ShortRunlog" resultMap="ShortRunlogResult">
        <include refid="selectShortRunlogVo"/>
        <where>  
            <if test="type != null  and type != ''"> and type like concat('%', #{type}, '%')</if>
            <if test="state != null  and state != ''"> and state = #{state}</if>
            <if test="content != null  and content != ''"> and content  like concat('%', #{content}, '%')</if>
            <if test="note != null  and note != ''"> and note = #{note}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        ORDER BY id desc
    </select>
    
    <select id="selectShortRunlogById" parameterType="Long" resultMap="ShortRunlogResult">
        <include refid="selectShortRunlogVo"/>
        where id = #{id}
    </select>

    <insert id="insertShortRunlog" parameterType="ShortRunlog" useGeneratedKeys="true" keyProperty="id">
        insert into short_runlog
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="type != null">type,</if>
            <if test="state != null">state,</if>
            <if test="content != null">content,</if>
            <if test="note != null">note,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="type != null">#{type},</if>
            <if test="state != null">#{state},</if>
            <if test="content != null">#{content},</if>
            <if test="note != null">#{note},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateShortRunlog" parameterType="ShortRunlog">
        update short_runlog
        <trim prefix="SET" suffixOverrides=",">
            <if test="type != null">type = #{type},</if>
            <if test="state != null">state = #{state},</if>
            <if test="content != null">content = #{content},</if>
            <if test="note != null">note = #{note},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteShortRunlogById" parameterType="Long">
        delete from short_runlog where id = #{id}
    </delete>

    <delete id="deleteShortRunlogByIds" parameterType="String">
        delete from short_runlog where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>