<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.ShortFeedbackMapper">
    
    <resultMap type="ShortFeedback" id="ShortFeedbackResult">
        <result property="id"    column="id"    />
        <result property="appId"    column="app_id"    />
        <result property="userId"    column="user_id"    />
        <result property="content"    column="content"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectShortFeedbackVo">
        select id, app_id, user_id, content, status, create_by, create_time, update_by, update_time, remark from short_feedback
    </sql>

    <select id="selectShortFeedbackList" parameterType="ShortFeedback" resultMap="ShortFeedbackResult">
        SELECT
        sf.id,
        sf.app_id,
        sf.user_id,
        sf.content,
        sf.STATUS,
        sf.create_by,
        sf.create_time,
        sf.update_by,
        sf.update_time,
        sf.remark,
        sa.`name` appName,
        su.username userName
        FROM
        short_feedback sf
        LEFT JOIN short_app sa on sf.app_id =sa.id
        LEFT JOIN short_user su on su.id = sf.user_id
        <where>  
            <if test="appId != null "> and sf.app_id = #{appId}</if>
            <if test="userId != null "> and sf.user_id = #{userId}</if>
            <if test="content != null  and content != ''"> and sf.content = #{content}</if>
            <if test="status != null  and status != ''"> and sf.status = #{status}</if>
        </where>
        ORDER BY sf.id DESC
    </select>
    
    <select id="selectShortFeedbackById" parameterType="Long" resultMap="ShortFeedbackResult">
        <include refid="selectShortFeedbackVo"/>
        where id = #{id}
    </select>

    <insert id="insertShortFeedback" parameterType="ShortFeedback" useGeneratedKeys="true" keyProperty="id">
        insert into short_feedback
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appId != null">app_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="content != null">content,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appId != null">#{appId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="content != null">#{content},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateShortFeedback" parameterType="ShortFeedback">
        update short_feedback
        <trim prefix="SET" suffixOverrides=",">
            <if test="appId != null">app_id = #{appId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="content != null">content = #{content},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteShortFeedbackById" parameterType="Long">
        delete from short_feedback where id = #{id}
    </delete>

    <delete id="deleteShortFeedbackByIds" parameterType="String">
        delete from short_feedback where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>