<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.ShortEmailSendLogMapper">

    <resultMap type="ShortEmailSendLog" id="ShortEmailSendLogResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="num"    column="num"    />
        <result property="email"    column="email"    />
        <result property="endTime"    column="end_time"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectShortEmailSendLogVo">
        select id, user_id, num, email, end_time, status, create_by, create_time, update_by, update_time, remark from short_email_send_log
    </sql>

    <select id="selectShortEmailSendLogList" parameterType="ShortEmailSendLog" resultMap="ShortEmailSendLogResult">
        <include refid="selectShortEmailSendLogVo"/>
        <where>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="num != null "> and num = #{num}</if>
            <if test="email != null  and email != ''"> and email = #{email}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>

    <select id="selectShortEmailSendLogById" parameterType="Long" resultMap="ShortEmailSendLogResult">
        <include refid="selectShortEmailSendLogVo"/>
        where id = #{id}
    </select>
    <select id="getListByEndTime" resultType="com.ruoyi.domain.ShortEmailSendLog">
        SELECT
        *
        FROM
        short_email_send_log
        WHERE
        end_time &lt;= NOW()
        and `status` = 0
        and num = 0
    </select>
    <select id="getByEmail" resultType="com.ruoyi.domain.ShortEmailSendLog">
        SELECT
            *
        FROM
            short_email_send_log WHERE email = #{email} LIMIT 1
    </select>

    <insert id="insertShortEmailSendLog" parameterType="ShortEmailSendLog">
        insert into short_email_send_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userId != null">user_id,</if>
            <if test="num != null">num,</if>
            <if test="email != null">email,</if>
            <if test="endTime != null">end_time,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userId != null">#{userId},</if>
            <if test="num != null">#{num},</if>
            <if test="email != null">#{email},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateShortEmailSendLog" parameterType="ShortEmailSendLog">
        update short_email_send_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="num != null">num = #{num},</if>
            <if test="email != null">email = #{email},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>
<update id="updateStatusByUserId" parameterType="ShortEmailSendLog">
    update short_email_send_log
    set status = 1 ,update_time=now(),update_by=#{userId}
    where user_id = #{userId}
</update>
    <delete id="deleteShortEmailSendLogById" parameterType="Long">
        delete from short_email_send_log where id = #{id}
    </delete>

    <delete id="deleteShortEmailSendLogByIds" parameterType="String">
        delete from short_email_send_log where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <update id="updateBatch">
        <if test="list !=null">
            <foreach collection="list" item="item" separator=";">
                update short_email_send_log set num=1 where id=#{item}
            </foreach>
        </if>
    </update>
    <select id="emailList" resultType="com.ruoyi.vo.ShortEmailSendLogVO">
        select email,e.user_id,num,o.id as orderId,l.name as linkName,pay_time
               o.link_id,amount,paymentMethod,link_time,o.status
        from short_email_send_log e left join short_order o on e.user_id = o.user_id
            left join short_sem_link l on o.link_id = l.id
        where 1=1
        <if test="sendLogDTO.email != null">and e.email like concat('%', #{sendLogDTO.email}, '%')</if>
        <if test="sendLogDTO.status != null">and o.status = #{sendLogDTO.status}</if>
        <if test="sendLogDTO.userId != null">and e.user_id = #{sendLogDTO.userId}</if>
        <if test="sendLogDTO.orderId != null">and o.id = #{sendLogDTO.orderId}</if>
        <if test="sendLogDTO.linkId != null">and l.id = #{sendLogDTO.linkId}</if>
        <if test="sendLogDTO.startDate != null">and date(o.link_time) &gt;= #{sendLogDTO.startDate}</if>
        <if test="sendLogDTO.endDate != null">and date(o.link_time) &lt;= #{sendLogDTO.endDate}</if>
        <if test="sendLogDTO.linkName != null">and l.name like concat('%', #{sendLogDTO.linkName}, '%')</if>
        <if test="sendLogDTO.status != null">and o.status = #{sendLogDTO.status}</if>
        order by link_time desc
    </select>
    <select id="emailListCount" resultType="java.lang.Integer">
       select count(*) from (select count(email)
        from short_email_send_log e left join short_order o on e.user_id = o.user_id
        left join short_sem_link l on o.link_id = l.id
        where 1=1
        <if test="sendLogDTO.email != null">and e.email like concat('%', #{sendLogDTO.email}, '%')</if>
        <if test="sendLogDTO.status != null">and o.status = #{sendLogDTO.status}</if>
        <if test="sendLogDTO.userId != null">and e.user_id = #{sendLogDTO.userId}</if>
        <if test="sendLogDTO.orderId != null">and o.id = #{sendLogDTO.orderId}</if>
        <if test="sendLogDTO.linkId != null">and l.link_id = #{sendLogDTO.linkId}</if>
        <if test="sendLogDTO.startDate != null">and date(o.link_time) &gt;= #{sendLogDTO.startDate}</if>
        <if test="sendLogDTO.endDate != null">and date(o.link_time) &lt;= #{sendLogDTO.endDate}</if>
        <if test="sendLogDTO.linkName != null">and l.name like concat('%', #{sendLogDTO.linkName}, '%')</if>

        order by link_time desc
        ) t
    </select>
</mapper>