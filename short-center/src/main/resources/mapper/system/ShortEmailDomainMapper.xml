<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.ShortEmailDomainMapper">

    <resultMap type="ShortEmailDomain" id="ShortEmailDomainResult">
        <result property="id"    column="id"    />
        <result property="appId"    column="app_id"    />
        <result property="domain"    column="domain"    />
        <result property="appName"    column="app_name"    />
        <result property="appDomain"    column="app_domain"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectShortEmailDomainVo">
        select id, app_id, domain, app_name, app_domain, status, create_by, create_time, update_by, update_time, remark from short_email_domain
    </sql>

    <select id="selectShortEmailDomainList" parameterType="ShortEmailDomain" resultMap="ShortEmailDomainResult">
        <include refid="selectShortEmailDomainVo"/>
        <where>
            <if test="appId != null "> and app_id = #{appId}</if>
            <if test="domain != null  and domain != ''"> and domain = #{domain}</if>
            <if test="appName != null  and appName != ''"> and app_name like concat('%', #{appName}, '%')</if>
            <if test="appDomain != null  and appDomain != ''"> and app_domain = #{appDomain}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>

    <select id="selectShortEmailDomainById" parameterType="Long" resultMap="ShortEmailDomainResult">
        <include refid="selectShortEmailDomainVo"/>
        where id = #{id}
    </select>
    <select id="getrandomDomain" resultType="com.ruoyi.domain.ShortEmailDomain">
        SELECT * FROM short_email_domain
        WHERE app_id = #{appId}
        ORDER BY RAND()
            LIMIT 1
    </select>


    <insert id="insertShortEmailDomain" parameterType="ShortEmailDomain" useGeneratedKeys="true" keyProperty="id">
        insert into short_email_domain
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appId != null">app_id,</if>
            <if test="domain != null">domain,</if>
            <if test="appName != null">app_name,</if>
            <if test="appDomain != null">app_domain,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appId != null">#{appId},</if>
            <if test="domain != null">#{domain},</if>
            <if test="appName != null">#{appName},</if>
            <if test="appDomain != null">#{appDomain},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateShortEmailDomain" parameterType="ShortEmailDomain">
        update short_email_domain
        <trim prefix="SET" suffixOverrides=",">
            <if test="appId != null">app_id = #{appId},</if>
            <if test="domain != null">domain = #{domain},</if>
            <if test="appName != null">app_name = #{appName},</if>
            <if test="appDomain != null">app_domain = #{appDomain},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteShortEmailDomainById" parameterType="Long">
        delete from short_email_domain where id = #{id}
    </delete>

    <delete id="deleteShortEmailDomainByIds" parameterType="String">
        delete from short_email_domain where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>